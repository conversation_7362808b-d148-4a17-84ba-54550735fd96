import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/inspection_report.dart';

class FirebaseService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Stream of auth state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      debugPrint('🔥 Starting Google Sign-In process...');

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        debugPrint('🔥 Google Sign-In was cancelled by user');
        return null;
      }

      debugPrint('🔥 Google user obtained: ${googleUser.email}');

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      debugPrint(
        '🔥 Google auth obtained - AccessToken: ${googleAuth.accessToken != null ? "✓" : "✗"}, IdToken: ${googleAuth.idToken != null ? "✓" : "✗"}',
      );

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      debugPrint('🔥 Firebase credential created, attempting sign in...');

      // Sign in with credential
      final userCredential = await _auth.signInWithCredential(credential);

      debugPrint(
        '🔥 Firebase sign-in successful: ${userCredential.user?.email}',
      );

      return userCredential;
    } catch (e, stackTrace) {
      debugPrint('🔥 Error signing in with Google: $e');
      debugPrint('🔥 Stack trace: $stackTrace');
      return null;
    }
  }

  // Sign out
  Future<void> signOut() async {
    await _googleSignIn.signOut();
    await _auth.signOut();
  }

  // Firestore operations for inspection reports
  static const String _reportsCollection = 'inspection_reports';

  // Add a new inspection report
  Future<void> addInspectionReport(InspectionReport report) async {
    try {
      await _firestore
          .collection(_reportsCollection)
          .doc(report.id)
          .set(report.toMap());
      debugPrint('🔥 Report saved to Firestore: ${report.id}');
    } catch (e) {
      debugPrint('🔥 Error saving report to Firestore: $e');
      rethrow;
    }
  }

  // Update an existing inspection report
  Future<void> updateInspectionReport(InspectionReport report) async {
    try {
      await _firestore
          .collection(_reportsCollection)
          .doc(report.id)
          .update(report.toMap());
      debugPrint('🔥 Report updated in Firestore: ${report.id}');
    } catch (e) {
      debugPrint('🔥 Error updating report in Firestore: $e');
      rethrow;
    }
  }

  // Get all inspection reports
  Future<List<InspectionReport>> getAllInspectionReports() async {
    try {
      final querySnapshot =
          await _firestore.collection(_reportsCollection).get();
      return querySnapshot.docs
          .map((doc) => InspectionReport.fromMap(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('🔥 Error getting reports from Firestore: $e');
      return [];
    }
  }

  // Get reports by article code
  Future<List<InspectionReport>> getReportsByArticleCode(
    String articleCode,
  ) async {
    try {
      final querySnapshot =
          await _firestore
              .collection(_reportsCollection)
              .where('articleCode', isEqualTo: articleCode)
              .get();
      return querySnapshot.docs
          .map((doc) => InspectionReport.fromMap(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('🔥 Error getting reports by article code from Firestore: $e');
      return [];
    }
  }

  // Delete an inspection report
  Future<void> deleteInspectionReport(String reportId) async {
    try {
      await _firestore.collection(_reportsCollection).doc(reportId).delete();
      debugPrint('🔥 Report deleted from Firestore: $reportId');
    } catch (e) {
      debugPrint('🔥 Error deleting report from Firestore: $e');
      rethrow;
    }
  }

  // Stream of inspection reports (real-time updates)
  Stream<List<InspectionReport>> getInspectionReportsStream() {
    return _firestore
        .collection(_reportsCollection)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => InspectionReport.fromMap(doc.data()))
                  .toList(),
        );
  }
}
