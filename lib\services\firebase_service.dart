import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/inspection_report.dart';

class FirebaseService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Stream of auth state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      debugPrint('🔥 Starting Google Sign-In process...');

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        debugPrint('🔥 Google Sign-In was cancelled by user');
        return null;
      }

      debugPrint('🔥 Google user obtained: ${googleUser.email}');

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      debugPrint(
        '🔥 Google auth obtained - AccessToken: ${googleAuth.accessToken != null ? "✓" : "✗"}, IdToken: ${googleAuth.idToken != null ? "✓" : "✗"}',
      );

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      debugPrint('🔥 Firebase credential created, attempting sign in...');

      // Sign in with credential
      final userCredential = await _auth.signInWithCredential(credential);

      debugPrint(
        '🔥 Firebase sign-in successful: ${userCredential.user?.email}',
      );

      return userCredential;
    } catch (e, stackTrace) {
      debugPrint('🔥 Error signing in with Google: $e');
      debugPrint('🔥 Stack trace: $stackTrace');
      return null;
    }
  }

  // Sign out
  Future<void> signOut() async {
    await _googleSignIn.signOut();
    await _auth.signOut();
  }

  // Firestore operations for inspection reports
  static const String _reportsCollection = 'inspection_reports';

  // Add a new inspection report
  Future<void> addInspectionReport(InspectionReport report) async {
    try {
      await _firestore
          .collection(_reportsCollection)
          .doc(report.id)
          .set(report.toMap());
      debugPrint('🔥 Report saved to Firestore: ${report.id}');
    } catch (e) {
      debugPrint('🔥 Error saving report to Firestore: $e');
      rethrow;
    }
  }

  // Update an existing inspection report
  Future<void> updateInspectionReport(InspectionReport report) async {
    try {
      await _firestore
          .collection(_reportsCollection)
          .doc(report.id)
          .update(report.toMap());
      debugPrint('🔥 Report updated in Firestore: ${report.id}');
    } catch (e) {
      debugPrint('🔥 Error updating report in Firestore: $e');
      rethrow;
    }
  }

  // Get all inspection reports with pagination
  Future<List<InspectionReport>> getAllInspectionReports({
    int limit = 50,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection(_reportsCollection)
          .orderBy('date', descending: true)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs
          .map(
            (doc) =>
                InspectionReport.fromMap(doc.data() as Map<String, dynamic>),
          )
          .toList();
    } catch (e) {
      debugPrint('🔥 Error getting reports from Firestore: $e');
      return [];
    }
  }

  // Get reports with client-side filtering (temporary solution until indexes are created)
  Future<List<InspectionReport>> getFilteredReports({
    InspectionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      debugPrint(
        '🔍 [getFilteredReports] Called with status: $status, startDate: $startDate, endDate: $endDate',
      );
      debugPrint('🔍 Getting all reports for client-side filtering');

      // Get all reports without complex queries to avoid index issues
      final querySnapshot =
          await _firestore.collection(_reportsCollection).get();

      debugPrint('🔍 Query returned ${querySnapshot.docs.length} documents');

      List<InspectionReport> reports =
          querySnapshot.docs
              .map(
                (doc) => InspectionReport.fromMap(
                  doc.data() as Map<String, dynamic>,
                ),
              )
              .toList();

      // Apply client-side filtering
      reports =
          reports.where((report) {
            // Filter by status
            if (status != null && report.status != status) {
              return false;
            }

            // Filter by date range
            if (startDate != null && report.date.isBefore(startDate)) {
              return false;
            }

            if (endDate != null &&
                report.date.isAfter(endDate.add(const Duration(days: 1)))) {
              return false;
            }

            return true;
          }).toList();

      // Sort by date descending (client-side)
      reports.sort((a, b) => b.date.compareTo(a.date));

      // Limit results
      if (reports.length > limit) {
        reports = reports.take(limit).toList();
      }

      debugPrint('🔍 Final filtered results: ${reports.length} documents');
      for (final report in reports) {
        debugPrint(
          '🔍 Report ${report.id}: status = ${report.status.toString().split('.').last}',
        );
      }

      return reports;
    } catch (e) {
      debugPrint('🔥 Error getting filtered reports from Firestore: $e');
      return [];
    }
  }

  // Get reports count for pagination info (simplified to avoid index issues)
  Future<int> getReportsCount({
    InspectionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // For now, get all documents and count client-side to avoid index issues
      // This is temporary until Firebase indexes are created
      final querySnapshot =
          await _firestore.collection(_reportsCollection).get();

      if (status == null && startDate == null && endDate == null) {
        return querySnapshot.docs.length;
      }

      // Client-side filtering for count
      int count = 0;
      for (final doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final report = InspectionReport.fromMap(data);

        // Apply filters
        bool matches = true;

        if (status != null && report.status != status) {
          matches = false;
        }

        if (startDate != null && report.date.isBefore(startDate)) {
          matches = false;
        }

        if (endDate != null &&
            report.date.isAfter(endDate.add(const Duration(days: 1)))) {
          matches = false;
        }

        if (matches) {
          count++;
        }
      }

      debugPrint('🔍 Total count after filtering: $count');
      return count;
    } catch (e) {
      debugPrint('🔥 Error getting reports count from Firestore: $e');
      return 0;
    }
  }

  // Get reports by article code
  Future<List<InspectionReport>> getReportsByArticleCode(
    String articleCode,
  ) async {
    try {
      final querySnapshot =
          await _firestore
              .collection(_reportsCollection)
              .where('articleCode', isEqualTo: articleCode)
              .get();
      return querySnapshot.docs
          .map((doc) => InspectionReport.fromMap(doc.data()))
          .toList();
    } catch (e) {
      debugPrint('🔥 Error getting reports by article code from Firestore: $e');
      return [];
    }
  }

  // Delete an inspection report
  Future<void> deleteInspectionReport(String reportId) async {
    try {
      await _firestore.collection(_reportsCollection).doc(reportId).delete();
      debugPrint('🔥 Report deleted from Firestore: $reportId');
    } catch (e) {
      debugPrint('🔥 Error deleting report from Firestore: $e');
      rethrow;
    }
  }

  // Stream of inspection reports (real-time updates)
  Stream<List<InspectionReport>> getInspectionReportsStream() {
    return _firestore
        .collection(_reportsCollection)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => InspectionReport.fromMap(doc.data()))
                  .toList(),
        );
  }
}
