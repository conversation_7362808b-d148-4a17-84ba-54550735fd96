import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:intl/intl.dart';
import '../models/inspection_report.dart';
import '../services/inspection_service.dart';

class ReportFormScreen extends StatefulWidget {
  const ReportFormScreen({Key? key}) : super(key: key);

  @override
  _ReportFormScreenState createState() => _ReportFormScreenState();
}

class _ReportFormScreenState extends State<ReportFormScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  final _inspectionService = InspectionService();
  bool _isLoading = false;
  bool _isEditing = false;
  InspectionReport? _existingReport;

  List<WorkmanshipComment> _workmanshipComments = [];
  List<MeasurementComment> _measurementComments = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final report =
        ModalRoute.of(context)?.settings.arguments as InspectionReport?;
    if (report != null && _existingReport == null) {
      _existingReport = report;
      _isEditing = true;
      _workmanshipComments = List.from(report.workmanshipComments);
      _measurementComments = List.from(report.measurementComments);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Edit Inspection Report' : 'New Inspection Report',
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: FormBuilder(
                  key: _formKey,
                  initialValue:
                      _isEditing
                          ? {
                            'date': _existingReport!.date,
                            'articleCode': _existingReport!.articleCode,
                            'articleName': _existingReport!.articleName,
                            'vendor': _existingReport!.vendor,
                            'productionStatus':
                                _existingReport!.productionStatus,
                            'qtyOrder': _existingReport!.qtyOrder.toString(),
                            'qtyCheck': _existingReport!.qtyCheck.toString(),
                            'isInitial': _existingReport!.isInitial,
                            'isFinal': _existingReport!.isFinal,
                            'isTYM': _existingReport!.isTYM,
                            'materialComment': _existingReport!.materialComment,
                            'inspectorName': _existingReport!.inspectorName,
                            'vendorName': _existingReport!.vendorName,
                            'note': _existingReport!.note,
                            'status': _existingReport!.status,
                          }
                          : {
                            'date': DateTime.now(),
                            'isInitial': false,
                            'isFinal': false,
                            'isTYM': false,
                            'status': InspectionStatus.hold,
                          },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section
                      Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'INSPECTION REPORT',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: FormBuilderDateTimePicker(
                                      name: 'date',
                                      inputType: InputType.date,
                                      format: DateFormat('yyyy-MM-dd'),
                                      decoration: const InputDecoration(
                                        labelText: 'Date',
                                        border: OutlineInputBorder(),
                                      ),
                                      validator:
                                          FormBuilderValidators.required(),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: FormBuilderTextField(
                                      name: 'articleCode',
                                      decoration: const InputDecoration(
                                        labelText: 'Article Code',
                                        border: OutlineInputBorder(),
                                      ),
                                      validator:
                                          FormBuilderValidators.required(),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: FormBuilderTextField(
                                      name: 'articleName',
                                      decoration: const InputDecoration(
                                        labelText: 'Article Name/Item',
                                        border: OutlineInputBorder(),
                                      ),
                                      validator:
                                          FormBuilderValidators.required(),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: FormBuilderTextField(
                                      name: 'articleCode',
                                      decoration: const InputDecoration(
                                        labelText: 'Article Code',
                                        border: OutlineInputBorder(),
                                      ),
                                      validator:
                                          FormBuilderValidators.required(),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: FormBuilderTextField(
                                      name: 'articleName',
                                      decoration: const InputDecoration(
                                        labelText: 'Article Name/Item',
                                        border: OutlineInputBorder(),
                                      ),
                                      validator:
                                          FormBuilderValidators.required(),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: FormBuilderTextField(
                                      name: 'vendor',
                                      decoration: const InputDecoration(
                                        labelText: 'Vendor',
                                        border: OutlineInputBorder(),
                                      ),
                                      validator:
                                          FormBuilderValidators.required(),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: FormBuilderTextField(
                                      name: 'productionStatus',
                                      decoration: const InputDecoration(
                                        labelText: 'Production Status',
                                        border: OutlineInputBorder(),
                                      ),
                                      validator:
                                          FormBuilderValidators.required(),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: FormBuilderTextField(
                                      name: 'qtyOrder',
                                      decoration: const InputDecoration(
                                        labelText: 'Qty Order',
                                        border: OutlineInputBorder(),
                                      ),
                                      keyboardType: TextInputType.number,
                                      validator: FormBuilderValidators.compose([
                                        FormBuilderValidators.required(),
                                        FormBuilderValidators.numeric(),
                                      ]),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: FormBuilderTextField(
                                      name: 'qtyCheck',
                                      decoration: const InputDecoration(
                                        labelText: 'Qty Check',
                                        border: OutlineInputBorder(),
                                      ),
                                      keyboardType: TextInputType.number,
                                      validator: FormBuilderValidators.compose([
                                        FormBuilderValidators.required(),
                                        FormBuilderValidators.numeric(),
                                      ]),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: FormBuilderCheckbox(
                                      name: 'isInitial',
                                      title: const Text('Initial'),
                                    ),
                                  ),
                                  Expanded(
                                    child: FormBuilderCheckbox(
                                      name: 'isFinal',
                                      title: const Text('Final'),
                                    ),
                                  ),
                                  Expanded(
                                    child: FormBuilderCheckbox(
                                      name: 'isTYM',
                                      title: const Text('TYM'),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Material Comment Section
                      Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'MATERIAL COMMENT',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              FormBuilderTextField(
                                name: 'materialComment',
                                decoration: const InputDecoration(
                                  border: OutlineInputBorder(),
                                ),
                                maxLines: 3,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Workmanship Comment Section
                      Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                color: Colors.grey.shade800,
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  children: [
                                    const Expanded(
                                      child: Text(
                                        'WORKMANSHIP COMMENT',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    ElevatedButton.icon(
                                      icon: const Icon(Icons.add, size: 16),
                                      label: const Text('Add'),
                                      onPressed: _addWorkmanshipComment,
                                      style: ElevatedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        minimumSize: const Size(0, 32),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Header row
                              Container(
                                color: Colors.grey.shade800,
                                padding: const EdgeInsets.all(8.0),
                                child: const Row(
                                  children: [
                                    SizedBox(
                                      width: 40,
                                      child: Text(
                                        'No.',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 4,
                                      child: Column(
                                        children: [
                                          Text(
                                            'Co',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            'm',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            'me',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            'nt',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      width: 60,
                                      child: Text(
                                        'PJ',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 60,
                                      child: Text(
                                        'NPJ',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    SizedBox(width: 50),
                                  ],
                                ),
                              ),
                              // Data rows
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: _workmanshipComments.length,
                                itemBuilder: (context, index) {
                                  final comment = _workmanshipComments[index];
                                  return Container(
                                    decoration: BoxDecoration(
                                      border: Border(
                                        bottom: BorderSide(
                                          color: Colors.grey.shade300,
                                        ),
                                      ),
                                    ),
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      children: [
                                        SizedBox(
                                          width: 40,
                                          child: Text(
                                            '${index + 1}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 4,
                                          child: GestureDetector(
                                            onTap:
                                                () =>
                                                    _showWorkmanshipCommentDialog(
                                                      index,
                                                    ),
                                            child: Container(
                                              padding: const EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                  color: Colors.grey.shade400,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Text(
                                                comment.comment.isEmpty
                                                    ? 'Tap to add comment...'
                                                    : comment.comment,
                                                style: TextStyle(
                                                  color:
                                                      comment.comment.isEmpty
                                                          ? Colors.grey
                                                          : Colors.black,
                                                ),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        SizedBox(
                                          width: 60,
                                          child: Checkbox(
                                            value: comment.isPJ,
                                            onChanged: (value) {
                                              setState(() {
                                                _workmanshipComments[index] =
                                                    WorkmanshipComment(
                                                      comment: comment.comment,
                                                      isPJ: value ?? false,
                                                      isNPJ: comment.isNPJ,
                                                    );
                                              });
                                            },
                                          ),
                                        ),
                                        SizedBox(
                                          width: 60,
                                          child: Checkbox(
                                            value: comment.isNPJ,
                                            onChanged: (value) {
                                              setState(() {
                                                _workmanshipComments[index] =
                                                    WorkmanshipComment(
                                                      comment: comment.comment,
                                                      isPJ: comment.isPJ,
                                                      isNPJ: value ?? false,
                                                    );
                                              });
                                            },
                                          ),
                                        ),
                                        SizedBox(
                                          width: 50,
                                          child: IconButton(
                                            icon: const Icon(
                                              Icons.delete,
                                              color: Colors.red,
                                              size: 20,
                                            ),
                                            onPressed: () {
                                              setState(() {
                                                _workmanshipComments.removeAt(
                                                  index,
                                                );
                                              });
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Measurement Comment Section
                      Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                color: Colors.grey.shade800,
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  children: [
                                    const Expanded(
                                      child: Text(
                                        'MEASUREMENT COMMENT',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    ElevatedButton.icon(
                                      icon: const Icon(Icons.add, size: 16),
                                      label: const Text('Add'),
                                      onPressed: _addMeasurementComment,
                                      style: ElevatedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        minimumSize: const Size(0, 32),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Header row
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Container(
                                  color: Colors.grey.shade800,
                                  padding: const EdgeInsets.all(8.0),
                                  child: Row(
                                    children: [
                                      const SizedBox(
                                        width: 40,
                                        child: Text(
                                          'No.',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 150,
                                        child: Text(
                                          'Keterangan',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      for (int i = 0; i < 5; i++)
                                        SizedBox(
                                          width: 70,
                                          child: Text(
                                            '${20 + (i * 5)}L',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      const SizedBox(
                                        width: 80,
                                        child: Text(
                                          'Satuan: cm',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 50),
                                    ],
                                  ),
                                ),
                              ),
                              // Data rows
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Column(
                                  children:
                                      _measurementComments.asMap().entries.map((
                                        entry,
                                      ) {
                                        final index = entry.key;
                                        final measurement = entry.value;
                                        return Container(
                                          decoration: BoxDecoration(
                                            border: Border(
                                              bottom: BorderSide(
                                                color: Colors.grey.shade300,
                                              ),
                                            ),
                                          ),
                                          padding: const EdgeInsets.all(8.0),
                                          child: Row(
                                            children: [
                                              SizedBox(
                                                width: 40,
                                                child: Text(
                                                  '${index + 1}',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 150,
                                                child: GestureDetector(
                                                  onTap:
                                                      () =>
                                                          _showMeasurementCommentDialog(
                                                            index,
                                                          ),
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.all(
                                                          12,
                                                        ),
                                                    decoration: BoxDecoration(
                                                      border: Border.all(
                                                        color:
                                                            Colors
                                                                .grey
                                                                .shade400,
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            4,
                                                          ),
                                                    ),
                                                    child: Text(
                                                      measurement
                                                              .keterangan
                                                              .isEmpty
                                                          ? 'Tap to add...'
                                                          : measurement
                                                              .keterangan,
                                                      style: TextStyle(
                                                        color:
                                                            measurement
                                                                    .keterangan
                                                                    .isEmpty
                                                                ? Colors.grey
                                                                : Colors.black,
                                                      ),
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                              for (int i = 0; i < 5; i++)
                                                SizedBox(
                                                  width: 70,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                          right: 4.0,
                                                        ),
                                                    child: GestureDetector(
                                                      onTap:
                                                          () =>
                                                              _showMeasurementCommentDialog(
                                                                index,
                                                              ),
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets.all(
                                                              8,
                                                            ),
                                                        decoration: BoxDecoration(
                                                          border: Border.all(
                                                            color:
                                                                Colors
                                                                    .grey
                                                                    .shade400,
                                                          ),
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                4,
                                                              ),
                                                        ),
                                                        child: Text(
                                                          i <
                                                                  measurement
                                                                      .measurements
                                                                      .length
                                                              ? measurement
                                                                  .measurements[i]
                                                                  .toString()
                                                              : '0.0',
                                                          textAlign:
                                                              TextAlign.center,
                                                          style:
                                                              const TextStyle(
                                                                fontSize: 12,
                                                              ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              SizedBox(
                                                width: 50,
                                                child: IconButton(
                                                  icon: const Icon(
                                                    Icons.delete,
                                                    color: Colors.red,
                                                    size: 20,
                                                  ),
                                                  onPressed: () {
                                                    setState(() {
                                                      _measurementComments
                                                          .removeAt(index);
                                                    });
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Footer Section
                      Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Signature Section
                              Container(
                                color: Colors.grey.shade800,
                                padding: const EdgeInsets.all(8.0),
                                child: const Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        'INSPECTOR',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      child: Text(
                                        'VENDOR',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      child: Text(
                                        'NOTE',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.all(16.0),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Inspector Section
                                    Expanded(
                                      child: Column(
                                        children: [
                                          Container(
                                            height: 100,
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                color: Colors.grey.shade400,
                                              ),
                                            ),
                                            child: const Center(
                                              child: Text(
                                                'Signature Area',
                                                style: TextStyle(
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          FormBuilderTextField(
                                            name: 'inspectorName',
                                            decoration: const InputDecoration(
                                              labelText: 'Inspector Name',
                                              border: OutlineInputBorder(),
                                              isDense: true,
                                            ),
                                            validator:
                                                FormBuilderValidators.required(),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    // Vendor Section
                                    Expanded(
                                      child: Column(
                                        children: [
                                          Container(
                                            height: 100,
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                color: Colors.grey.shade400,
                                              ),
                                            ),
                                            child: const Center(
                                              child: Text(
                                                'Signature Area',
                                                style: TextStyle(
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          FormBuilderTextField(
                                            name: 'vendorName',
                                            decoration: const InputDecoration(
                                              labelText: 'Vendor Name',
                                              border: OutlineInputBorder(),
                                              isDense: true,
                                            ),
                                            validator:
                                                FormBuilderValidators.required(),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    // Note Section
                                    Expanded(
                                      child: Column(
                                        children: [
                                          FormBuilderTextField(
                                            name: 'note',
                                            decoration: const InputDecoration(
                                              labelText: 'Note',
                                              border: OutlineInputBorder(),
                                              isDense: true,
                                            ),
                                            maxLines: 3,
                                          ),
                                          const SizedBox(height: 16),
                                          FormBuilderRadioGroup(
                                            name: 'status',
                                            decoration: const InputDecoration(
                                              labelText: 'Status',
                                              border: InputBorder.none,
                                            ),
                                            options: const [
                                              FormBuilderFieldOption(
                                                value: InspectionStatus.pass,
                                                child: Text('☐ PASS'),
                                              ),
                                              FormBuilderFieldOption(
                                                value: InspectionStatus.hold,
                                                child: Text('☐ HOLD'),
                                              ),
                                              FormBuilderFieldOption(
                                                value: InspectionStatus.fail,
                                                child: Text('☐ FAIL'),
                                              ),
                                            ],
                                            validator:
                                                FormBuilderValidators.required(),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Submit Button
                      SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          onPressed: _submitForm,
                          child: Text(
                            _isEditing ? 'Update Report' : 'Save Report',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  void _addWorkmanshipComment() {
    _showWorkmanshipCommentDialog();
  }

  void _showWorkmanshipCommentDialog([int? editIndex]) {
    final isEditing = editIndex != null;
    final existingComment = isEditing ? _workmanshipComments[editIndex] : null;

    String comment = existingComment?.comment ?? '';
    bool isPJ = existingComment?.isPJ ?? false;
    bool isNPJ = existingComment?.isNPJ ?? false;

    final commentController = TextEditingController(text: comment);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                isEditing
                    ? 'Edit Workmanship Comment'
                    : 'Add Workmanship Comment',
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Comment:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: commentController,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Enter workmanship comment...',
                      ),
                      maxLines: 4,
                      onChanged: (value) {
                        comment = value;
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Status:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('PJ'),
                            value: isPJ,
                            onChanged: (value) {
                              setDialogState(() {
                                isPJ = value ?? false;
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        ),
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('NPJ'),
                            value: isNPJ,
                            onChanged: (value) {
                              setDialogState(() {
                                isNPJ = value ?? false;
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (comment.trim().isNotEmpty) {
                      setState(() {
                        final newComment = WorkmanshipComment(
                          comment: comment.trim(),
                          isPJ: isPJ,
                          isNPJ: isNPJ,
                        );

                        if (isEditing) {
                          _workmanshipComments[editIndex] = newComment;
                        } else {
                          _workmanshipComments.add(newComment);
                        }
                      });
                      Navigator.of(context).pop();
                    }
                  },
                  child: Text(isEditing ? 'Update' : 'Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _addMeasurementComment() {
    _showMeasurementCommentDialog();
  }

  void _showMeasurementCommentDialog([int? editIndex]) {
    final isEditing = editIndex != null;
    final existingComment = isEditing ? _measurementComments[editIndex] : null;

    String keterangan = existingComment?.keterangan ?? '';
    List<double> measurements =
        existingComment?.measurements ?? [0.0, 0.0, 0.0, 0.0, 0.0];

    final keteranganController = TextEditingController(text: keterangan);
    final measurementControllers = List.generate(
      5,
      (index) => TextEditingController(
        text:
            index < measurements.length
                ? measurements[index].toString()
                : '0.0',
      ),
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            isEditing ? 'Edit Measurement Comment' : 'Add Measurement Comment',
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Keterangan:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: keteranganController,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: 'Enter measurement description...',
                  ),
                  maxLines: 2,
                  onChanged: (value) {
                    keterangan = value;
                  },
                ),
                const SizedBox(height: 16),
                const Text(
                  'Measurements (cm):',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                for (int i = 0; i < 5; i++)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 60,
                          child: Text(
                            '${20 + (i * 5)}L:',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: measurementControllers[i],
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              isDense: true,
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            onChanged: (value) {
                              measurements[i] = double.tryParse(value) ?? 0.0;
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (keterangan.trim().isNotEmpty) {
                  setState(() {
                    final newComment = MeasurementComment(
                      number:
                          isEditing
                              ? existingComment!.number
                              : _measurementComments.length + 1,
                      keterangan: keterangan.trim(),
                      measurements: measurements,
                    );

                    if (isEditing) {
                      _measurementComments[editIndex] = newComment;
                    } else {
                      _measurementComments.add(newComment);
                    }
                  });
                  Navigator.of(context).pop();
                }
              },
              child: Text(isEditing ? 'Update' : 'Add'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        final formData = _formKey.currentState!.value;

        final report = InspectionReport(
          id:
              _isEditing
                  ? _existingReport!.id
                  : DateTime.now().millisecondsSinceEpoch.toString(),
          date: formData['date'] as DateTime,
          articleCode: formData['articleCode'] as String,
          articleName: formData['articleName'] as String,
          vendor: formData['vendor'] as String,
          productionStatus: formData['productionStatus'] as String,
          qtyOrder: int.parse(formData['qtyOrder']),
          qtyCheck: int.parse(formData['qtyCheck']),
          isInitial: formData['isInitial'] as bool,
          isFinal: formData['isFinal'] as bool,
          isTYM: formData['isTYM'] as bool,
          materialComment: formData['materialComment'] as String? ?? '',
          workmanshipComments: _workmanshipComments,
          measurementComments: _measurementComments,
          inspectorName: formData['inspectorName'] as String,
          vendorName: formData['vendorName'] as String,
          note: formData['note'] as String? ?? '',
          status: formData['status'] as InspectionStatus,
        );

        if (_isEditing) {
          await _inspectionService.updateReport(report);
        } else {
          await _inspectionService.addReport(report);
        }

        Navigator.pop(context, true);
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
