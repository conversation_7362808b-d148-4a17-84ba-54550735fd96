import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/inspection_report.dart';
import '../services/inspection_service.dart';
import '../services/firebase_service.dart';

class ReportListScreen extends StatefulWidget {
  const ReportListScreen({Key? key}) : super(key: key);

  @override
  _ReportListScreenState createState() => _ReportListScreenState();
}

class _ReportListScreenState extends State<ReportListScreen> {
  late InspectionService _inspectionService;
  bool _isLoading = true;
  List<String> _articleCodes = [];
  Map<String, List<InspectionReport>> _reportsByArticle = {};

  @override
  void initState() {
    super.initState();
    _inspectionService = InspectionService();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // Add mock data for demonstration
    _inspectionService.addMockData();

    // Get all reports
    final reports = _inspectionService.getAllReports();

    // Group reports by article code
    _articleCodes = _inspectionService.getUniqueArticleCodes();
    _reportsByArticle = {};

    for (final articleCode in _articleCodes) {
      _reportsByArticle[articleCode] = _inspectionService.getReportsByArticle(
        articleCode,
      );
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _exportToExcel(InspectionReport report) async {
    // For demo purposes, we'll just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Excel export would happen here for report: ${report.id}',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Inspection Reports'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              final firebaseService = Provider.of<FirebaseService>(
                context,
                listen: false,
              );
              await firebaseService.signOut();
              if (mounted) {
                Navigator.pushReplacementNamed(context, '/login');
              }
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _articleCodes.isEmpty
              ? const Center(child: Text('No reports found'))
              : ListView.builder(
                itemCount: _articleCodes.length,
                itemBuilder: (context, index) {
                  final articleCode = _articleCodes[index];
                  final reports = _reportsByArticle[articleCode] ?? [];

                  return ExpansionTile(
                    title: Text(
                      'Article: $articleCode',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text('${reports.length} reports'),
                    children:
                        reports.map((report) {
                          return ListTile(
                            title: Text(
                              'Date: ${report.date.toString().substring(0, 10)}',
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Article Name: ${report.articleName}'),
                                Text('Vendor: ${report.vendor}'),
                                Text(
                                  'Status: ${report.status.toString().split('.').last.toUpperCase()}',
                                ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(
                                    Icons.file_download,
                                    color: Colors.green,
                                  ),
                                  onPressed: () => _exportToExcel(report),
                                  tooltip: 'Download Excel',
                                ),
                                IconButton(
                                  icon: const Icon(
                                    Icons.visibility,
                                    color: Colors.blue,
                                  ),
                                  onPressed: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/report_form',
                                      arguments: report,
                                    );
                                  },
                                  tooltip: 'View Details',
                                ),
                              ],
                            ),
                            onTap: () {
                              Navigator.pushNamed(
                                context,
                                '/report_form',
                                arguments: report,
                              );
                            },
                          );
                        }).toList(),
                  );
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/report_form');
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
