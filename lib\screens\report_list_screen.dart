import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/inspection_report.dart';
import '../services/inspection_service.dart';
import '../services/firebase_service.dart';

class ReportListScreen extends StatefulWidget {
  const ReportListScreen({Key? key}) : super(key: key);

  @override
  _ReportListScreenState createState() => _ReportListScreenState();
}

class _ReportListScreenState extends State<ReportListScreen> {
  late InspectionService _inspectionService;
  bool _isLoading = true;
  List<String> _articleCodes = [];
  Map<String, List<InspectionReport>> _reportsByArticle = {};
  List<InspectionReport> _allReports = [];
  List<InspectionReport> _filteredReports = [];

  // Filter variables
  InspectionStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _showFilters = false;

  // Pagination variables
  bool _hasMoreData = true;
  bool _isLoadingMore = false;
  DocumentSnapshot? _lastDocument;
  int _totalCount = 0;
  final int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _inspectionService = InspectionService();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _filteredReports.clear();
      _lastDocument = null;
      _hasMoreData = true;
    });

    try {
      debugPrint('🔍 Starting to load data...');

      // Add mock data for demonstration
      await _inspectionService.addMockData();
      debugPrint('🔍 Mock data added');

      // Get total count for pagination info
      _totalCount = await _inspectionService.getReportsCount(
        status: _selectedStatus,
        startDate: _startDate,
        endDate: _endDate,
      );
      debugPrint('🔍 Total reports count: $_totalCount');

      // Load first page with server-side filtering
      await _loadMoreData();

      debugPrint('🔍 Initial data loaded: ${_filteredReports.length} reports');

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('❌ Error loading data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final newReports = await _inspectionService.getFilteredReports(
        status: _selectedStatus,
        startDate: _startDate,
        endDate: _endDate,
        limit: _pageSize,
        startAfter: _lastDocument,
      );

      if (newReports.isNotEmpty) {
        setState(() {
          _filteredReports.addAll(newReports);
          // Note: We would need to get the last document from Firebase service
          // For now, we'll disable pagination if we get less than pageSize
          _hasMoreData = newReports.length == _pageSize;
        });
      } else {
        setState(() {
          _hasMoreData = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading more data: $e');
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  void _applyFilters() {
    // Reload data with new filters (server-side filtering)
    _loadData();
  }

  void _clearFilters() {
    setState(() {
      _selectedStatus = null;
      _startDate = null;
      _endDate = null;
      _applyFilters();
    });
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange:
          _startDate != null && _endDate != null
              ? DateTimeRange(start: _startDate!, end: _endDate!)
              : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
        _applyFilters();
      });
    }
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Filters',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _clearFilters,
                child: const Text('Clear All'),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Status Filter
          Row(
            children: [
              const Text(
                'Status: ',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButton<InspectionStatus?>(
                  value: _selectedStatus,
                  hint: const Text('All Status'),
                  isExpanded: true,
                  items: [
                    const DropdownMenuItem<InspectionStatus?>(
                      value: null,
                      child: Text('All Status'),
                    ),
                    ...InspectionStatus.values.map((status) {
                      return DropdownMenuItem<InspectionStatus?>(
                        value: status,
                        child: Text(
                          status.toString().split('.').last.toUpperCase(),
                        ),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                      _applyFilters();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Date Range Filter
          Row(
            children: [
              const Text(
                'Date Range: ',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _selectDateRange,
                  icon: const Icon(Icons.date_range, size: 16),
                  label: Text(
                    _startDate != null && _endDate != null
                        ? '${_startDate!.toString().substring(0, 10)} - ${_endDate!.toString().substring(0, 10)}'
                        : 'Select Date Range',
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade50,
                    foregroundColor: Colors.blue.shade700,
                    elevation: 0,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Results count
          Text(
            'Showing ${_filteredReports.length} of $_totalCount reports',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(InspectionReport report) {
    Color statusColor;
    switch (report.status) {
      case InspectionStatus.pass:
        statusColor = Colors.green;
        break;
      case InspectionStatus.hold:
        statusColor = Colors.orange;
        break;
      case InspectionStatus.fail:
        statusColor = Colors.red;
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 4,
          height: double.infinity,
          decoration: BoxDecoration(
            color: statusColor,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                '${report.articleCode} - ${report.articleName}',
                style: const TextStyle(fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: statusColor.withOpacity(0.3)),
              ),
              child: Text(
                report.status.toString().split('.').last.toUpperCase(),
                style: TextStyle(
                  color: statusColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              'Date: ${report.date.toString().substring(0, 10)}',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              'Vendor: ${report.vendor}',
              style: const TextStyle(fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              'Qty: ${report.qtyCheck}/${report.qtyOrder}',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              'Report By: ${report.reportBy ?? 'Unknown User'}',
              style: const TextStyle(fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(
                Icons.file_download,
                color: Colors.green,
                size: 20,
              ),
              onPressed: () => _exportToExcel(report),
              tooltip: 'Download Excel',
            ),
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.blue, size: 20),
              onPressed: () {
                Navigator.pushNamed(context, '/report_form', arguments: report);
              },
              tooltip: 'View Details',
            ),
          ],
        ),
        onTap: () {
          Navigator.pushNamed(context, '/report_form', arguments: report);
        },
      ),
    );
  }

  Future<void> _exportToExcel(InspectionReport report) async {
    // For demo purposes, we'll just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Excel export would happen here for report: ${report.id}',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Inspection Reports'),
        actions: [
          IconButton(
            icon: Icon(
              _showFilters ? Icons.filter_list_off : Icons.filter_list,
            ),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
            tooltip: 'Toggle Filters',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              final firebaseService = Provider.of<FirebaseService>(
                context,
                listen: false,
              );
              await firebaseService.signOut();
              if (mounted) {
                Navigator.pushReplacementNamed(context, '/login');
              }
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Filter UI
                  if (_showFilters) _buildFilterSection(),

                  // Reports List
                  Expanded(
                    child:
                        _filteredReports.isEmpty
                            ? const Center(child: Text('No reports found'))
                            : ListView.builder(
                              itemCount: _filteredReports.length,
                              itemBuilder: (context, index) {
                                final report = _filteredReports[index];
                                return _buildReportCard(report);
                              },
                            ),
                  ),
                ],
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/report_form');
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
