import 'package:flutter/foundation.dart';

class InspectionReport {
  final String id;
  final DateTime date;
  final String? code; // Changed from articleCode - can be PO or article code
  final String articleName;
  final String? product; // New field - Product/Item
  final String vendor;
  final double? repairPercentage; // New field - %Repair
  final String productionStatus;
  final int qtyOrder;
  final String qtyCheck; // Changed from int to String for flexibility
  final bool isInline; // Changed from isInitial to isInline
  final bool isFinal;
  final String? customCheckLabel; // Dynamic checkbox label (replaces TYM)
  final bool isCustomCheck; // Dynamic checkbox value (replaces isTYM)
  final String materialComment;
  final List<WorkmanshipComment> workmanshipComments;
  final List<MeasurementComment> measurementComments;
  final List<ChecklistItem> checklistItems;
  final String note;
  final InspectionStatus status;
  final String? inspectorSignature;
  final String? vendorSignature;
  final String? reportBy;

  InspectionReport({
    required this.id,
    required this.date,
    this.code,
    required this.articleName,
    this.product,
    required this.vendor,
    this.repairPercentage,
    required this.productionStatus,
    required this.qtyOrder,
    required this.qtyCheck,
    required this.isInline,
    required this.isFinal,
    this.customCheckLabel,
    required this.isCustomCheck,
    required this.materialComment,
    required this.workmanshipComments,
    required this.measurementComments,
    required this.checklistItems,
    required this.note,
    required this.status,
    this.reportBy,
    this.inspectorSignature,
    this.vendorSignature,
  });

  // Convert to Map for Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'code': code,
      'articleName': articleName,
      'product': product,
      'vendor': vendor,
      'repairPercentage': repairPercentage,
      'productionStatus': productionStatus,
      'qtyOrder': qtyOrder,
      'qtyCheck': qtyCheck,
      'isInline': isInline, // Changed from isInitial
      'isFinal': isFinal,
      'customCheckLabel': customCheckLabel, // Dynamic checkbox label
      'isCustomCheck': isCustomCheck, // Dynamic checkbox value
      'materialComment': materialComment,
      'workmanshipComments':
          workmanshipComments.map((comment) => comment.toMap()).toList(),
      'measurementComments':
          measurementComments.map((comment) => comment.toMap()).toList(),
      'checklistItems': checklistItems.map((item) => item.toMap()).toList(),
      'note': note,
      'status': status.toString().split('.').last,
      'reportBy': reportBy,
      'inspectorSignature': inspectorSignature,
      'vendorSignature': vendorSignature,
    };
  }

  // Create from Map from Firebase
  factory InspectionReport.fromMap(Map<String, dynamic> map) {
    // Debug logging for backward compatibility
    if (map['code'] == null && map['articleCode'] != null) {
      debugPrint(
        '🔄 Converting old data: articleCode "${map['articleCode']}" → code',
      );
    }

    return InspectionReport(
      id: map['id'],
      date: DateTime.parse(map['date']),
      code:
          map['code'] ??
          map['articleCode'], // Backward compatibility - allow null
      articleName: map['articleName'],
      product: map['product'] ?? '', // Default for backward compatibility
      vendor: map['vendor'],
      repairPercentage:
          (map['repairPercentage'] ?? 0.0)
              .toDouble(), // Default for backward compatibility
      productionStatus: map['productionStatus'],
      qtyOrder: map['qtyOrder'],
      qtyCheck:
          map['qtyCheck'] is int
              ? map['qtyCheck'].toString()
              : map['qtyCheck'], // Handle both int and String
      isInline:
          map['isInline'] ??
          map['isInitial'] ??
          false, // Backward compatibility
      isFinal: map['isFinal'],
      customCheckLabel:
          map['customCheckLabel'] ??
          map['customCheckboxLabel'] ??
          (map['isTYM'] != null
              ? 'PPM'
              : null), // Backward compatibility: isTYM → isCustomCheck
      isCustomCheck:
          map['isCustomCheck'] ??
          map['customCheckboxValue'] ??
          map['isTYM'] ??
          false, // Backward compatibility: isTYM → isCustomCheck
      materialComment: map['materialComment'],
      workmanshipComments: List<WorkmanshipComment>.from(
        map['workmanshipComments']?.map((x) => WorkmanshipComment.fromMap(x)) ??
            [],
      ),
      measurementComments: List<MeasurementComment>.from(
        map['measurementComments']?.map((x) => MeasurementComment.fromMap(x)) ??
            [],
      ),
      checklistItems: List<ChecklistItem>.from(
        map['checklistItems']?.map((x) => ChecklistItem.fromMap(x)) ?? [],
      ),
      note: map['note'],
      status: _statusFromString(map['status']),
      reportBy: map['reportBy'] ?? 'Unknown User',
      inspectorSignature: map['inspectorSignature'],
      vendorSignature: map['vendorSignature'],
    );
  }

  static InspectionStatus _statusFromString(String status) {
    switch (status) {
      case 'pass':
        return InspectionStatus.pass;
      case 'hold':
        return InspectionStatus.hold;
      case 'fail':
        return InspectionStatus.fail;
      default:
        return InspectionStatus.hold;
    }
  }
}

class WorkmanshipComment {
  final String comment;
  final bool isMJ; // Changed from isPJ to isMJ
  final bool isMN; // Changed from isNPJ to isMN

  WorkmanshipComment({
    required this.comment,
    required this.isMJ,
    required this.isMN,
  });

  Map<String, dynamic> toMap() {
    return {'comment': comment, 'isMJ': isMJ, 'isMN': isMN};
  }

  factory WorkmanshipComment.fromMap(Map<String, dynamic> map) {
    // Backward compatibility: support both old (PJ/NPJ) and new (MJ/MN) field names
    return WorkmanshipComment(
      comment: map['comment'],
      isMJ: map['isMJ'] ?? map['isPJ'] ?? false, // Support old isPJ field
      isMN: map['isMN'] ?? map['isNPJ'] ?? false, // Support old isNPJ field
    );
  }
}

class MeasurementComment {
  final int number;
  final String keterangan;
  final List<double> measurements;
  final String unit; // Added dynamic unit field

  MeasurementComment({
    required this.number,
    required this.keterangan,
    required this.measurements,
    this.unit = 'cm', // Default unit is cm
  });

  Map<String, dynamic> toMap() {
    return {
      'number': number,
      'keterangan': keterangan,
      'measurements': measurements,
      'unit': unit,
    };
  }

  factory MeasurementComment.fromMap(Map<String, dynamic> map) {
    return MeasurementComment(
      number: map['number'],
      keterangan: map['keterangan'],
      measurements: List<double>.from(map['measurements']),
      unit: map['unit'] ?? 'cm', // Backward compatibility with default cm
    );
  }
}

class ChecklistItem {
  final String label;
  final bool isChecked;

  ChecklistItem({required this.label, required this.isChecked});

  Map<String, dynamic> toMap() {
    return {'label': label, 'isChecked': isChecked};
  }

  factory ChecklistItem.fromMap(Map<String, dynamic> map) {
    return ChecklistItem(label: map['label'], isChecked: map['isChecked']);
  }
}

enum InspectionStatus { pass, hold, fail }
