class InspectionReport {
  final String id;
  final DateTime date;
  final String articleCode;
  final String articleName;
  final String vendor;
  final String productionStatus;
  final int qtyOrder;
  final int qtyCheck;
  final bool isInitial;
  final bool isFinal;
  final bool isTYM;
  final String materialComment;
  final List<WorkmanshipComment> workmanshipComments;
  final List<MeasurementComment> measurementComments;
  final List<ChecklistItem> checklistItems;
  final String inspectorName;
  final String vendorName;
  final String note;
  final InspectionStatus status;
  final String? inspectorSignature;
  final String? vendorSignature;

  InspectionReport({
    required this.id,
    required this.date,
    required this.articleCode,
    required this.articleName,
    required this.vendor,
    required this.productionStatus,
    required this.qtyOrder,
    required this.qtyCheck,
    required this.isInitial,
    required this.isFinal,
    required this.isTYM,
    required this.materialComment,
    required this.workmanshipComments,
    required this.measurementComments,
    required this.checklistItems,
    required this.inspectorName,
    required this.vendorName,
    required this.note,
    required this.status,
    this.inspectorSignature,
    this.vendorSignature,
  });

  // Convert to Map for Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'articleCode': articleCode,
      'articleName': articleName,
      'vendor': vendor,
      'productionStatus': productionStatus,
      'qtyOrder': qtyOrder,
      'qtyCheck': qtyCheck,
      'isInitial': isInitial,
      'isFinal': isFinal,
      'isTYM': isTYM,
      'materialComment': materialComment,
      'workmanshipComments':
          workmanshipComments.map((comment) => comment.toMap()).toList(),
      'measurementComments':
          measurementComments.map((comment) => comment.toMap()).toList(),
      'checklistItems': checklistItems.map((item) => item.toMap()).toList(),
      'inspectorName': inspectorName,
      'vendorName': vendorName,
      'note': note,
      'status': status.toString().split('.').last,
      'inspectorSignature': inspectorSignature,
      'vendorSignature': vendorSignature,
    };
  }

  // Create from Map from Firebase
  factory InspectionReport.fromMap(Map<String, dynamic> map) {
    return InspectionReport(
      id: map['id'],
      date: DateTime.parse(map['date']),
      articleCode: map['articleCode'],
      articleName: map['articleName'],
      vendor: map['vendor'],
      productionStatus: map['productionStatus'],
      qtyOrder: map['qtyOrder'],
      qtyCheck: map['qtyCheck'],
      isInitial: map['isInitial'],
      isFinal: map['isFinal'],
      isTYM: map['isTYM'],
      materialComment: map['materialComment'],
      workmanshipComments: List<WorkmanshipComment>.from(
        map['workmanshipComments']?.map((x) => WorkmanshipComment.fromMap(x)) ??
            [],
      ),
      measurementComments: List<MeasurementComment>.from(
        map['measurementComments']?.map((x) => MeasurementComment.fromMap(x)) ??
            [],
      ),
      checklistItems: List<ChecklistItem>.from(
        map['checklistItems']?.map((x) => ChecklistItem.fromMap(x)) ?? [],
      ),
      inspectorName: map['inspectorName'],
      vendorName: map['vendorName'],
      note: map['note'],
      status: _statusFromString(map['status']),
      inspectorSignature: map['inspectorSignature'],
      vendorSignature: map['vendorSignature'],
    );
  }

  static InspectionStatus _statusFromString(String status) {
    switch (status) {
      case 'pass':
        return InspectionStatus.pass;
      case 'hold':
        return InspectionStatus.hold;
      case 'fail':
        return InspectionStatus.fail;
      default:
        return InspectionStatus.hold;
    }
  }
}

class WorkmanshipComment {
  final String comment;
  final bool isPJ;
  final bool isNPJ;

  WorkmanshipComment({
    required this.comment,
    required this.isPJ,
    required this.isNPJ,
  });

  Map<String, dynamic> toMap() {
    return {'comment': comment, 'isPJ': isPJ, 'isNPJ': isNPJ};
  }

  factory WorkmanshipComment.fromMap(Map<String, dynamic> map) {
    return WorkmanshipComment(
      comment: map['comment'],
      isPJ: map['isPJ'],
      isNPJ: map['isNPJ'],
    );
  }
}

class MeasurementComment {
  final int number;
  final String keterangan;
  final List<double> measurements;

  MeasurementComment({
    required this.number,
    required this.keterangan,
    required this.measurements,
  });

  Map<String, dynamic> toMap() {
    return {
      'number': number,
      'keterangan': keterangan,
      'measurements': measurements,
    };
  }

  factory MeasurementComment.fromMap(Map<String, dynamic> map) {
    return MeasurementComment(
      number: map['number'],
      keterangan: map['keterangan'],
      measurements: List<double>.from(map['measurements']),
    );
  }
}

class ChecklistItem {
  final String label;
  final bool isChecked;

  ChecklistItem({required this.label, required this.isChecked});

  Map<String, dynamic> toMap() {
    return {'label': label, 'isChecked': isChecked};
  }

  factory ChecklistItem.fromMap(Map<String, dynamic> map) {
    return ChecklistItem(label: map['label'], isChecked: map['isChecked']);
  }
}

enum InspectionStatus { pass, hold, fail }
