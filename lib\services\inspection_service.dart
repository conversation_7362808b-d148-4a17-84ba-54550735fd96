import 'dart:io';
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/inspection_report.dart';
import 'firebase_service.dart';

class InspectionService {
  final FirebaseService _firebaseService = FirebaseService();

  // Mock data for demonstration (fallback)
  List<InspectionReport> _reports = [];

  // Get all reports with pagination
  Future<List<InspectionReport>> getAllReports({
    int limit = 50,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      return await _firebaseService.getAllInspectionReports(
        limit: limit,
        startAfter: startAfter,
      );
    } catch (e) {
      debugPrint('Error getting reports from Firebase, using local data: $e');
      return _reports;
    }
  }

  // Get filtered reports with server-side filtering
  Future<List<InspectionReport>> getFilteredReports({
    InspectionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      return await _firebaseService.getFilteredReports(
        status: status,
        startDate: startDate,
        endDate: endDate,
        limit: limit,
        startAfter: startAfter,
      );
    } catch (e) {
      debugPrint(
        'Error getting filtered reports from Firebase, using local data: $e',
      );
      // Fallback to local filtering
      List<InspectionReport> filtered =
          _reports.where((report) {
            if (status != null && report.status != status) {
              return false;
            }
            if (startDate != null && report.date.isBefore(startDate)) {
              return false;
            }
            if (endDate != null &&
                report.date.isAfter(endDate.add(const Duration(days: 1)))) {
              return false;
            }
            return true;
          }).toList();

      filtered.sort((a, b) => b.date.compareTo(a.date));
      return filtered.take(limit).toList();
    }
  }

  // Get reports count
  Future<int> getReportsCount({
    InspectionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _firebaseService.getReportsCount(
        status: status,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      debugPrint('Error getting reports count from Firebase: $e');
      return _reports.length;
    }
  }

  // Get reports by article code
  Future<List<InspectionReport>> getReportsByArticle(String articleCode) async {
    try {
      return await _firebaseService.getReportsByArticleCode(articleCode);
    } catch (e) {
      debugPrint(
        'Error getting reports by article from Firebase, using local data: $e',
      );
      return _reports.where((report) => report.code == articleCode).toList();
    }
  }

  // Get unique article codes
  Future<List<String>> getUniqueArticleCodes() async {
    try {
      final reports = await _firebaseService.getAllInspectionReports();
      return reports.map((report) => report.code ?? 'N/A').toSet().toList();
    } catch (e) {
      debugPrint(
        'Error getting unique article codes from Firebase, using local data: $e',
      );
      return _reports.map((report) => report.code ?? 'N/A').toSet().toList();
    }
  }

  // Add a new report
  Future<void> addReport(InspectionReport report) async {
    try {
      await _firebaseService.addInspectionReport(report);
      debugPrint('🔥 Report successfully saved to Firestore');
    } catch (e) {
      debugPrint('🔥 Error saving to Firebase, saving locally: $e');
      _reports.add(report);
      rethrow;
    }
  }

  // Update an existing report
  Future<void> updateReport(InspectionReport updatedReport) async {
    try {
      await _firebaseService.updateInspectionReport(updatedReport);
      debugPrint('🔥 Report successfully updated in Firestore');
    } catch (e) {
      debugPrint('🔥 Error updating in Firebase, updating locally: $e');
      final index = _reports.indexWhere(
        (report) => report.id == updatedReport.id,
      );
      if (index != -1) {
        _reports[index] = updatedReport;
      }
      rethrow;
    }
  }

  // Delete a report
  Future<void> deleteReport(String reportId) async {
    try {
      await _firebaseService.deleteInspectionReport(reportId);
      debugPrint('🔥 Report successfully deleted from Firestore');
    } catch (e) {
      debugPrint('🔥 Error deleting from Firebase, deleting locally: $e');
      _reports.removeWhere((report) => report.id == reportId);
      rethrow;
    }
  }

  // Get reports stream for real-time updates
  Stream<List<InspectionReport>> getReportsStream() {
    return _firebaseService.getInspectionReportsStream();
  }

  // Export report to Excel
  Future<String?> exportReportToExcel(InspectionReport report) async {
    try {
      // Request storage permission
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        if (!status.isGranted) {
          return null;
        }
      }

      // Create Excel document
      final excel = Excel.createExcel();
      final sheet = excel['Inspection Report'];

      // Add headers
      sheet.cell(CellIndex.indexByString("A1")).value = TextCellValue("Date");
      sheet.cell(CellIndex.indexByString("B1")).value = TextCellValue(
        "Article Code",
      );
      sheet.cell(CellIndex.indexByString("C1")).value = TextCellValue(
        "Article Name",
      );
      sheet.cell(CellIndex.indexByString("D1")).value = TextCellValue("Vendor");
      sheet.cell(CellIndex.indexByString("E1")).value = TextCellValue(
        "Production Status",
      );
      sheet.cell(CellIndex.indexByString("F1")).value = TextCellValue(
        "Qty Order",
      );
      sheet.cell(CellIndex.indexByString("G1")).value = TextCellValue(
        "Qty Check",
      );
      sheet.cell(CellIndex.indexByString("H1")).value = TextCellValue(
        "Inspection Type",
      );
      sheet.cell(CellIndex.indexByString("I1")).value = TextCellValue(
        "Material Comment",
      );
      sheet.cell(CellIndex.indexByString("J1")).value = TextCellValue(
        "Inspector",
      );
      sheet.cell(CellIndex.indexByString("K1")).value = TextCellValue(
        "Vendor Name",
      );
      sheet.cell(CellIndex.indexByString("L1")).value = TextCellValue("Note");
      sheet.cell(CellIndex.indexByString("M1")).value = TextCellValue("Status");

      // Add data
      sheet.cell(CellIndex.indexByString("A2")).value = TextCellValue(
        report.date.toString(),
      );
      sheet.cell(CellIndex.indexByString("B2")).value = TextCellValue(
        report.code ?? 'N/A',
      );
      sheet.cell(CellIndex.indexByString("C2")).value = TextCellValue(
        report.articleName,
      );
      sheet.cell(CellIndex.indexByString("D2")).value = TextCellValue(
        report.vendor,
      );
      sheet.cell(CellIndex.indexByString("E2")).value = TextCellValue(
        report.productionStatus,
      );
      sheet.cell(CellIndex.indexByString("F2")).value = IntCellValue(
        report.qtyOrder,
      );
      sheet.cell(CellIndex.indexByString("G2")).value = TextCellValue(
        report.qtyCheck,
      );

      String inspectionType = "";
      if (report.isInline) inspectionType += "Inline ";
      if (report.isFinal) inspectionType += "Final ";
      if (report.isCustomCheck && report.customCheckLabel != null) {
        inspectionType += "${report.customCheckLabel} ";
      }
      sheet.cell(CellIndex.indexByString("H2")).value = TextCellValue(
        inspectionType.trim(),
      );

      sheet.cell(CellIndex.indexByString("I2")).value = TextCellValue(
        report.materialComment,
      );
      sheet.cell(CellIndex.indexByString("J2")).value = TextCellValue(
        report.inspectorName,
      );
      sheet.cell(CellIndex.indexByString("K2")).value = TextCellValue(
        report.vendorName,
      );
      sheet.cell(CellIndex.indexByString("L2")).value = TextCellValue(
        report.note,
      );
      sheet.cell(CellIndex.indexByString("M2")).value = TextCellValue(
        report.status.toString().split('.').last,
      );

      // Add workmanship comments in a new sheet
      final workmanshipSheet = excel['Workmanship'];
      workmanshipSheet
          .cell(CellIndex.indexByString("A1"))
          .value = TextCellValue("Comment");
      workmanshipSheet
          .cell(CellIndex.indexByString("B1"))
          .value = TextCellValue("PJ");
      workmanshipSheet
          .cell(CellIndex.indexByString("C1"))
          .value = TextCellValue("NPJ");

      for (var i = 0; i < report.workmanshipComments.length; i++) {
        final comment = report.workmanshipComments[i];
        workmanshipSheet
            .cell(CellIndex.indexByString("A${i + 2}"))
            .value = TextCellValue(comment.comment);
        workmanshipSheet
            .cell(CellIndex.indexByString("B${i + 2}"))
            .value = TextCellValue(comment.isMJ ? "Yes" : "No");
        workmanshipSheet
            .cell(CellIndex.indexByString("C${i + 2}"))
            .value = TextCellValue(comment.isMN ? "Yes" : "No");
      }

      // Add measurement comments in a new sheet
      final measurementSheet = excel['Measurements'];
      measurementSheet
          .cell(CellIndex.indexByString("A1"))
          .value = TextCellValue("No.");
      measurementSheet
          .cell(CellIndex.indexByString("B1"))
          .value = TextCellValue("Keterangan");

      // Add measurement columns (assuming max 10 measurements)
      for (var i = 0; i < 10; i++) {
        measurementSheet
            .cell(CellIndex.indexByString("${String.fromCharCode(67 + i)}1"))
            .value = TextCellValue("Measurement ${i + 1}");
      }

      for (var i = 0; i < report.measurementComments.length; i++) {
        final measurement = report.measurementComments[i];
        measurementSheet
            .cell(CellIndex.indexByString("A${i + 2}"))
            .value = IntCellValue(measurement.number);
        measurementSheet
            .cell(CellIndex.indexByString("B${i + 2}"))
            .value = TextCellValue(measurement.keterangan);

        for (var j = 0; j < measurement.measurements.length && j < 10; j++) {
          measurementSheet
              .cell(
                CellIndex.indexByString(
                  "${String.fromCharCode(67 + j)}${i + 2}",
                ),
              )
              .value = DoubleCellValue(measurement.measurements[j]);
        }
      }

      // Save the Excel file
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'inspection_report_${report.code ?? 'N_A'}_${report.date.millisecondsSinceEpoch}.xlsx';
      final filePath = '${directory.path}/$fileName';

      final fileBytes = excel.save();
      if (fileBytes != null) {
        File(filePath)
          ..createSync(recursive: true)
          ..writeAsBytesSync(fileBytes);

        return filePath;
      }

      return null;
    } catch (e) {
      debugPrint('Error exporting to Excel: $e');
      return null;
    }
  }

  // Add some mock data for testing
  Future<void> addMockData() async {
    // Check if mock data already exists
    try {
      final existingReports = await _firebaseService.getAllInspectionReports();
      if (existingReports.length >= 3) {
        debugPrint('🔍 Mock data already exists, skipping...');
        // Check and fix any legacy data
        await _fixLegacyData(existingReports);
        return;
      }
    } catch (e) {
      debugPrint(
        '🔍 Error checking existing data, proceeding with mock data: $e',
      );
    }

    final mockReports = [
      InspectionReport(
        id: '1',
        date: DateTime.now(),
        code: 'PO-2024-001',
        articleName: 'T-Shirt',
        product: 'Cotton Basic Tee',
        vendor: 'Vendor A',
        repairPercentage: 2.5,
        productionStatus: 'In Progress',
        qtyOrder: 100,
        qtyCheck: '20',
        isInline: true,
        isFinal: false,
        customCheckLabel: 'PPM',
        isCustomCheck: false,
        materialComment: 'Good quality material',
        workmanshipComments: [
          WorkmanshipComment(
            comment: 'Stitching is good',
            isMJ: true,
            isMN: false,
          ),
        ],
        measurementComments: [
          MeasurementComment(
            number: 1,
            keterangan: 'Length',
            measurements: [70.5, 71.0, 70.8],
          ),
        ],
        checklistItems: [
          ChecklistItem(label: 'Size Spec', isChecked: true),
          ChecklistItem(label: 'Fabric Quality', isChecked: true),
          ChecklistItem(label: 'Color Matching', isChecked: false),
          ChecklistItem(label: 'Stitching Quality', isChecked: true),
        ],
        inspectorName: 'John Doe',
        vendorName: 'Vendor A Rep',
        note: 'Approved with minor adjustments',
        status: InspectionStatus.pass,
        reportBy: '<EMAIL>',
      ),
      InspectionReport(
        id: '2',
        date: DateTime.now().subtract(Duration(days: 1)),
        code: 'ART-T001',
        articleName: 'T-Shirt',
        product: 'Premium Cotton Tee',
        vendor: 'Vendor A',
        repairPercentage: 5.0,
        productionStatus: 'Completed',
        qtyOrder: 100,
        qtyCheck: '30',
        isInline: false,
        isFinal: true,
        customCheckLabel: 'PPM',
        isCustomCheck: true,
        materialComment: 'Material meets specifications',
        workmanshipComments: [
          WorkmanshipComment(
            comment: 'Collar needs adjustment',
            isMJ: false,
            isMN: true,
          ),
        ],
        measurementComments: [
          MeasurementComment(
            number: 1,
            keterangan: 'Length',
            measurements: [70.2, 70.5, 70.3],
          ),
        ],
        checklistItems: [
          ChecklistItem(label: 'Size Spec', isChecked: false),
          ChecklistItem(label: 'Fabric Quality', isChecked: true),
          ChecklistItem(label: 'Color Matching', isChecked: true),
          ChecklistItem(label: 'Packaging', isChecked: false),
        ],
        inspectorName: 'Jane Smith',
        vendorName: 'Vendor A Rep',
        note: 'Minor issues to be fixed',
        status: InspectionStatus.hold,
        reportBy: '<EMAIL>',
      ),
      InspectionReport(
        id: '3',
        date: DateTime.now().subtract(Duration(days: 2)),
        code: 'PO-2024-002',
        articleName: 'Pants',
        product: 'Denim Jeans',
        vendor: 'Vendor B',
        repairPercentage: 1.2,
        productionStatus: 'In Progress',
        qtyOrder: 200,
        qtyCheck: '40',
        isInline: true,
        isFinal: false,
        customCheckLabel: 'Quality Check',
        isCustomCheck: false,
        materialComment: 'Fabric quality is excellent',
        workmanshipComments: [
          WorkmanshipComment(
            comment: 'Zipper installation is perfect',
            isMJ: true,
            isMN: false,
          ),
        ],
        measurementComments: [
          MeasurementComment(
            number: 1,
            keterangan: 'Waist',
            measurements: [82.0, 82.5, 82.3],
          ),
        ],
        checklistItems: [
          ChecklistItem(label: 'Size Spec', isChecked: true),
          ChecklistItem(label: 'Fabric Quality', isChecked: true),
          ChecklistItem(label: 'Stitching Quality', isChecked: true),
          ChecklistItem(label: 'Labeling', isChecked: true),
          ChecklistItem(label: 'Packaging', isChecked: false),
        ],
        inspectorName: 'John Doe',
        vendorName: 'Vendor B Rep',
        note: 'Approved',
        status: InspectionStatus.pass,
        reportBy: '<EMAIL>',
      ),
    ];

    // Add mock reports to Firestore
    for (final report in mockReports) {
      try {
        await _firebaseService.addInspectionReport(report);
        debugPrint(
          '🔍 Added mock report: ${report.id} - ${report.code ?? 'N/A'}',
        );
      } catch (e) {
        debugPrint('🔍 Error adding mock report ${report.id}: $e');
        // Fallback to local storage
        _reports.add(report);
      }
    }

    debugPrint('🔍 Mock data setup completed');
  }

  // Fix legacy data that might have null code field
  Future<void> _fixLegacyData(List<InspectionReport> reports) async {
    for (final report in reports) {
      if (report.code == null || report.code!.isEmpty) {
        debugPrint('🔧 Fixing legacy report ${report.id} with null code');

        // Create updated report with default code
        final updatedReport = InspectionReport(
          id: report.id,
          date: report.date,
          code: 'LEGACY-${report.id}', // Default code for legacy data
          articleName: report.articleName,
          product: report.product ?? 'Unknown Product',
          vendor: report.vendor,
          repairPercentage: report.repairPercentage ?? 0.0,
          productionStatus: report.productionStatus,
          qtyOrder: report.qtyOrder,
          qtyCheck: report.qtyCheck,
          isInline: report.isInline,
          isFinal: report.isFinal,
          customCheckLabel: report.customCheckLabel ?? 'PPM',
          isCustomCheck: report.isCustomCheck,
          materialComment: report.materialComment,
          workmanshipComments: report.workmanshipComments,
          measurementComments: report.measurementComments,
          checklistItems: report.checklistItems,
          inspectorName: report.inspectorName,
          vendorName: report.vendorName,
          note: report.note,
          status: report.status,
          reportBy: report.reportBy,
          inspectorSignature: report.inspectorSignature,
          vendorSignature: report.vendorSignature,
        );

        try {
          await _firebaseService.updateInspectionReport(updatedReport);
          debugPrint('🔧 Fixed legacy report ${report.id}');
        } catch (e) {
          debugPrint('🔧 Error fixing legacy report ${report.id}: $e');
        }
      }
    }
  }
}
