import 'dart:io';
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/inspection_report.dart';
import 'firebase_service.dart';

class InspectionService {
  final FirebaseService _firebaseService = FirebaseService();

  // Mock data for demonstration (fallback)
  List<InspectionReport> _reports = [];

  // Get all reports
  Future<List<InspectionReport>> getAllReports() async {
    try {
      return await _firebaseService.getAllInspectionReports();
    } catch (e) {
      debugPrint('Error getting reports from Firebase, using local data: $e');
      return _reports;
    }
  }

  // Get reports by article code
  Future<List<InspectionReport>> getReportsByArticle(String articleCode) async {
    try {
      return await _firebaseService.getReportsByArticleCode(articleCode);
    } catch (e) {
      debugPrint(
        'Error getting reports by article from Firebase, using local data: $e',
      );
      return _reports
          .where((report) => report.articleCode == articleCode)
          .toList();
    }
  }

  // Get unique article codes
  Future<List<String>> getUniqueArticleCodes() async {
    try {
      final reports = await _firebaseService.getAllInspectionReports();
      return reports.map((report) => report.articleCode).toSet().toList();
    } catch (e) {
      debugPrint(
        'Error getting unique article codes from Firebase, using local data: $e',
      );
      return _reports.map((report) => report.articleCode).toSet().toList();
    }
  }

  // Add a new report
  Future<void> addReport(InspectionReport report) async {
    try {
      await _firebaseService.addInspectionReport(report);
      debugPrint('🔥 Report successfully saved to Firestore');
    } catch (e) {
      debugPrint('🔥 Error saving to Firebase, saving locally: $e');
      _reports.add(report);
      rethrow;
    }
  }

  // Update an existing report
  Future<void> updateReport(InspectionReport updatedReport) async {
    try {
      await _firebaseService.updateInspectionReport(updatedReport);
      debugPrint('🔥 Report successfully updated in Firestore');
    } catch (e) {
      debugPrint('🔥 Error updating in Firebase, updating locally: $e');
      final index = _reports.indexWhere(
        (report) => report.id == updatedReport.id,
      );
      if (index != -1) {
        _reports[index] = updatedReport;
      }
      rethrow;
    }
  }

  // Delete a report
  Future<void> deleteReport(String reportId) async {
    try {
      await _firebaseService.deleteInspectionReport(reportId);
      debugPrint('🔥 Report successfully deleted from Firestore');
    } catch (e) {
      debugPrint('🔥 Error deleting from Firebase, deleting locally: $e');
      _reports.removeWhere((report) => report.id == reportId);
      rethrow;
    }
  }

  // Get reports stream for real-time updates
  Stream<List<InspectionReport>> getReportsStream() {
    return _firebaseService.getInspectionReportsStream();
  }

  // Export report to Excel
  Future<String?> exportReportToExcel(InspectionReport report) async {
    try {
      // Request storage permission
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        if (!status.isGranted) {
          return null;
        }
      }

      // Create Excel document
      final excel = Excel.createExcel();
      final sheet = excel['Inspection Report'];

      // Add headers
      sheet.cell(CellIndex.indexByString("A1")).value = TextCellValue("Date");
      sheet.cell(CellIndex.indexByString("B1")).value = TextCellValue(
        "Article Code",
      );
      sheet.cell(CellIndex.indexByString("C1")).value = TextCellValue(
        "Article Name",
      );
      sheet.cell(CellIndex.indexByString("D1")).value = TextCellValue("Vendor");
      sheet.cell(CellIndex.indexByString("E1")).value = TextCellValue(
        "Production Status",
      );
      sheet.cell(CellIndex.indexByString("F1")).value = TextCellValue(
        "Qty Order",
      );
      sheet.cell(CellIndex.indexByString("G1")).value = TextCellValue(
        "Qty Check",
      );
      sheet.cell(CellIndex.indexByString("H1")).value = TextCellValue(
        "Inspection Type",
      );
      sheet.cell(CellIndex.indexByString("I1")).value = TextCellValue(
        "Material Comment",
      );
      sheet.cell(CellIndex.indexByString("J1")).value = TextCellValue(
        "Inspector",
      );
      sheet.cell(CellIndex.indexByString("K1")).value = TextCellValue(
        "Vendor Name",
      );
      sheet.cell(CellIndex.indexByString("L1")).value = TextCellValue("Note");
      sheet.cell(CellIndex.indexByString("M1")).value = TextCellValue("Status");

      // Add data
      sheet.cell(CellIndex.indexByString("A2")).value = TextCellValue(
        report.date.toString(),
      );
      sheet.cell(CellIndex.indexByString("B2")).value = TextCellValue(
        report.articleCode,
      );
      sheet.cell(CellIndex.indexByString("C2")).value = TextCellValue(
        report.articleName,
      );
      sheet.cell(CellIndex.indexByString("D2")).value = TextCellValue(
        report.vendor,
      );
      sheet.cell(CellIndex.indexByString("E2")).value = TextCellValue(
        report.productionStatus,
      );
      sheet.cell(CellIndex.indexByString("F2")).value = IntCellValue(
        report.qtyOrder,
      );
      sheet.cell(CellIndex.indexByString("G2")).value = IntCellValue(
        report.qtyCheck,
      );

      String inspectionType = "";
      if (report.isInitial) inspectionType += "Initial ";
      if (report.isFinal) inspectionType += "Final ";
      if (report.isTYM) inspectionType += "TYM ";
      sheet.cell(CellIndex.indexByString("H2")).value = TextCellValue(
        inspectionType.trim(),
      );

      sheet.cell(CellIndex.indexByString("I2")).value = TextCellValue(
        report.materialComment,
      );
      sheet.cell(CellIndex.indexByString("J2")).value = TextCellValue(
        report.inspectorName,
      );
      sheet.cell(CellIndex.indexByString("K2")).value = TextCellValue(
        report.vendorName,
      );
      sheet.cell(CellIndex.indexByString("L2")).value = TextCellValue(
        report.note,
      );
      sheet.cell(CellIndex.indexByString("M2")).value = TextCellValue(
        report.status.toString().split('.').last,
      );

      // Add workmanship comments in a new sheet
      final workmanshipSheet = excel['Workmanship'];
      workmanshipSheet
          .cell(CellIndex.indexByString("A1"))
          .value = TextCellValue("Comment");
      workmanshipSheet
          .cell(CellIndex.indexByString("B1"))
          .value = TextCellValue("PJ");
      workmanshipSheet
          .cell(CellIndex.indexByString("C1"))
          .value = TextCellValue("NPJ");

      for (var i = 0; i < report.workmanshipComments.length; i++) {
        final comment = report.workmanshipComments[i];
        workmanshipSheet
            .cell(CellIndex.indexByString("A${i + 2}"))
            .value = TextCellValue(comment.comment);
        workmanshipSheet
            .cell(CellIndex.indexByString("B${i + 2}"))
            .value = TextCellValue(comment.isPJ ? "Yes" : "No");
        workmanshipSheet
            .cell(CellIndex.indexByString("C${i + 2}"))
            .value = TextCellValue(comment.isNPJ ? "Yes" : "No");
      }

      // Add measurement comments in a new sheet
      final measurementSheet = excel['Measurements'];
      measurementSheet
          .cell(CellIndex.indexByString("A1"))
          .value = TextCellValue("No.");
      measurementSheet
          .cell(CellIndex.indexByString("B1"))
          .value = TextCellValue("Keterangan");

      // Add measurement columns (assuming max 10 measurements)
      for (var i = 0; i < 10; i++) {
        measurementSheet
            .cell(CellIndex.indexByString("${String.fromCharCode(67 + i)}1"))
            .value = TextCellValue("Measurement ${i + 1}");
      }

      for (var i = 0; i < report.measurementComments.length; i++) {
        final measurement = report.measurementComments[i];
        measurementSheet
            .cell(CellIndex.indexByString("A${i + 2}"))
            .value = IntCellValue(measurement.number);
        measurementSheet
            .cell(CellIndex.indexByString("B${i + 2}"))
            .value = TextCellValue(measurement.keterangan);

        for (var j = 0; j < measurement.measurements.length && j < 10; j++) {
          measurementSheet
              .cell(
                CellIndex.indexByString(
                  "${String.fromCharCode(67 + j)}${i + 2}",
                ),
              )
              .value = DoubleCellValue(measurement.measurements[j]);
        }
      }

      // Save the Excel file
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'inspection_report_${report.articleCode}_${report.date.millisecondsSinceEpoch}.xlsx';
      final filePath = '${directory.path}/$fileName';

      final fileBytes = excel.save();
      if (fileBytes != null) {
        File(filePath)
          ..createSync(recursive: true)
          ..writeAsBytesSync(fileBytes);

        return filePath;
      }

      return null;
    } catch (e) {
      debugPrint('Error exporting to Excel: $e');
      return null;
    }
  }

  // Add some mock data for testing
  void addMockData() {
    _reports = [
      InspectionReport(
        id: '1',
        date: DateTime.now(),
        articleCode: 'A001',
        articleName: 'T-Shirt',
        vendor: 'Vendor A',
        productionStatus: 'In Progress',
        qtyOrder: 100,
        qtyCheck: 20,
        isInitial: true,
        isFinal: false,
        isTYM: false,
        materialComment: 'Good quality material',
        workmanshipComments: [
          WorkmanshipComment(
            comment: 'Stitching is good',
            isPJ: true,
            isNPJ: false,
          ),
        ],
        measurementComments: [
          MeasurementComment(
            number: 1,
            keterangan: 'Length',
            measurements: [70.5, 71.0, 70.8],
          ),
        ],
        checklistItems: [
          ChecklistItem(label: 'Size Spec', isChecked: true),
          ChecklistItem(label: 'Fabric Quality', isChecked: true),
          ChecklistItem(label: 'Color Matching', isChecked: false),
          ChecklistItem(label: 'Stitching Quality', isChecked: true),
        ],
        inspectorName: 'John Doe',
        vendorName: 'Vendor A Rep',
        note: 'Approved with minor adjustments',
        status: InspectionStatus.pass,
      ),
      InspectionReport(
        id: '2',
        date: DateTime.now().subtract(Duration(days: 1)),
        articleCode: 'A001',
        articleName: 'T-Shirt',
        vendor: 'Vendor A',
        productionStatus: 'Completed',
        qtyOrder: 100,
        qtyCheck: 30,
        isInitial: false,
        isFinal: true,
        isTYM: false,
        materialComment: 'Material meets specifications',
        workmanshipComments: [
          WorkmanshipComment(
            comment: 'Collar needs adjustment',
            isPJ: false,
            isNPJ: true,
          ),
        ],
        measurementComments: [
          MeasurementComment(
            number: 1,
            keterangan: 'Length',
            measurements: [70.2, 70.5, 70.3],
          ),
        ],
        checklistItems: [
          ChecklistItem(label: 'Size Spec', isChecked: false),
          ChecklistItem(label: 'Fabric Quality', isChecked: true),
          ChecklistItem(label: 'Color Matching', isChecked: true),
          ChecklistItem(label: 'Packaging', isChecked: false),
        ],
        inspectorName: 'Jane Smith',
        vendorName: 'Vendor A Rep',
        note: 'Minor issues to be fixed',
        status: InspectionStatus.hold,
      ),
      InspectionReport(
        id: '3',
        date: DateTime.now().subtract(Duration(days: 2)),
        articleCode: 'B002',
        articleName: 'Pants',
        vendor: 'Vendor B',
        productionStatus: 'In Progress',
        qtyOrder: 200,
        qtyCheck: 40,
        isInitial: true,
        isFinal: false,
        isTYM: false,
        materialComment: 'Fabric quality is excellent',
        workmanshipComments: [
          WorkmanshipComment(
            comment: 'Zipper installation is perfect',
            isPJ: true,
            isNPJ: false,
          ),
        ],
        measurementComments: [
          MeasurementComment(
            number: 1,
            keterangan: 'Waist',
            measurements: [82.0, 82.5, 82.3],
          ),
        ],
        checklistItems: [
          ChecklistItem(label: 'Size Spec', isChecked: true),
          ChecklistItem(label: 'Fabric Quality', isChecked: true),
          ChecklistItem(label: 'Stitching Quality', isChecked: true),
          ChecklistItem(label: 'Labeling', isChecked: true),
          ChecklistItem(label: 'Packaging', isChecked: false),
        ],
        inspectorName: 'John Doe',
        vendorName: 'Vendor B Rep',
        note: 'Approved',
        status: InspectionStatus.pass,
      ),
    ];
  }
}
