# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# Android related
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/key.properties
*.jks

# Web related
lib/generated_plugin_registrant.dart

# Windows related
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugin_registrant.h
**/windows/flutter/generated_plugins.cmake

# Linux related
**/linux/flutter/generated_plugin_registrant.cc
**/linux/flutter/generated_plugin_registrant.h
**/linux/flutter/generated_plugins.cmake

# macOS related
**/macos/Flutter/GeneratedPluginRegistrant.swift
**/macos/Flutter/ephemeral/
**/macos/xcuserdata/

# Coverage
coverage/

# Exceptions to above rules.
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages

# Firebase related
**/ios/firebase_app_id_file.json
**/ios/GoogleService-Info.plist
**/android/app/google-services.json
.firebase/
**/ios/Runner/GoogleService-Info.plist

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# API Keys and secrets
**/lib/config/api_keys.dart
**/lib/config/secrets.dart
secrets.json
api_keys.json

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test related
test_driver/
integration_test/
test/coverage/

# Generated files
*.g.dart
*.freezed.dart
*.gr.dart
*.config.dart

# L10n
/lib/l10n/

# Analysis
.dart_tool/dartpad/web_plugin_registrant.dart

# FVM Version Cache
.fvm/

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Xcode
**/ios/Runner.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings

# Gradle
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# Keystore files
*.keystore
*.jks

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# Build artifacts
*.apk
*.aab
*.ipa
*.app

# Backup files
*.bak
*.backup
*~

# Temporary files
*.tmp
*.temp

# Archive files
*.zip
*.tar.gz
*.rar

# IDE files
*.swp
*.swo
*~

# Package files
*.jar
*.war
*.ear

# virtual machine crash logs
hs_err_pid*

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Crashlytics
**/ios/**/Crashlytics.framework
**/ios/**/Fabric.framework

# Flutter repo-specific
/bin/cache/
/bin/internal/bootstrap.bat
/bin/internal/bootstrap.sh
/bin/mingit/
/dev/benchmarks/mega_gallery/
/dev/bots/.recipe_deps
/dev/bots/android_tools/
/dev/devicelab/ABresults*.json
/dev/docs/doc/
/dev/docs/flutter.docs.zip
/dev/docs/lib/
/dev/docs/pubspec.yaml
/dev/integration_tests/**/xcuserdata
/dev/integration_tests/**/Pods
/packages/flutter/coverage/
version
analysis_benchmark.json

# Flutter build-related
**/build/

# Dart pub related
.packages
.pub/
# pubspec.lock # Uncomment this line if you want to ignore pubspec.lock

# Flutter version management
.fvm/flutter_sdk

# Melos
melos_*.yaml
.melos_tool/

# Mason
.mason/

# Very Good CLI
.vgv/
