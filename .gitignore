# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# Android related
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/key.properties
*.jks

# Web related
lib/generated_plugin_registrant.dart

# Windows related
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugin_registrant.h
**/windows/flutter/generated_plugins.cmake

# Linux related
**/linux/flutter/generated_plugin_registrant.cc
**/linux/flutter/generated_plugin_registrant.h
**/linux/flutter/generated_plugins.cmake

# macOS related
**/macos/Flutter/GeneratedPluginRegistrant.swift
**/macos/Flutter/ephemeral/
**/macos/xcuserdata/

# Coverage
coverage/

# Exceptions to above rules.
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages

# Firebase related
**/ios/firebase_app_id_file.json
**/ios/GoogleService-Info.plist
**/android/app/google-services.json
.firebase/
**/ios/Runner/GoogleService-Info.plist
lib/firebase_options.dart
firebase_options.dart
.firebaserc
firebase.json

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# API Keys and secrets
**/lib/config/api_keys.dart
**/lib/config/secrets.dart
secrets.json
api_keys.json

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test related
test_driver/
integration_test/
test/coverage/

# Generated files
*.g.dart
*.freezed.dart
*.gr.dart
*.config.dart

# L10n
/lib/l10n/

# Analysis
.dart_tool/dartpad/web_plugin_registrant.dart

# FVM Version Cache
.fvm/

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Xcode
**/ios/Runner.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings

# Gradle
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# Keystore files
*.keystore
*.jks

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# Build artifacts
*.apk
*.aab
*.ipa
*.app

# Backup files
*.bak
*.backup
*~

# Temporary files
*.tmp
*.temp

# Archive files
*.zip
*.tar.gz
*.rar

# IDE files
*.swp
*.swo
*~

# Package files
*.jar
*.war
*.ear

# virtual machine crash logs
hs_err_pid*

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Crashlytics
**/ios/**/Crashlytics.framework
**/ios/**/Fabric.framework

# Flutter repo-specific
/bin/cache/
/bin/internal/bootstrap.bat
/bin/internal/bootstrap.sh
/bin/mingit/
/dev/benchmarks/mega_gallery/
/dev/bots/.recipe_deps
/dev/bots/android_tools/
/dev/devicelab/ABresults*.json
/dev/docs/doc/
/dev/docs/flutter.docs.zip
/dev/docs/lib/
/dev/docs/pubspec.yaml
/dev/integration_tests/**/xcuserdata
/dev/integration_tests/**/Pods
/packages/flutter/coverage/
version
analysis_benchmark.json

# Flutter build-related
**/build/

# Dart pub related
.packages
.pub/
# pubspec.lock # Uncomment this line if you want to ignore pubspec.lock

# Flutter version management
.fvm/flutter_sdk

# Melos
melos_*.yaml
.melos_tool/

# Mason
.mason/

# Very Good CLI
.vgv/

# Additional Flutter/Dart files
*.lock
.flutter-plugins-dependencies
.metadata

# Platform specific generated files
**/android/app/src/main/java/**/MainActivity.kt
**/android/app/src/main/kotlin/**/MainActivity.kt
**/ios/Runner/AppDelegate.swift
**/ios/Runner/Runner-Bridging-Header.h

# Build outputs
*.dSYM.zip
*.dSYM

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Instrumented libs generated by jscoverage/JSCover
lib-cov

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node_modules is already covered above but adding for completeness
node_modules

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional eslint cache
.eslintcache

# Snowpack dependency directory
web_modules/

# Rollup cache
.rollup.cache/

# Parcel default cache directory
.parcel-cache

# Vite cache
.vite

# Svelte kit
.svelte-kit

# Remix
build/
public/build/

# PlanetScale
.pscale

# Turborepo
.turbo

# Vercel
.vercel

# Contentlayer
.contentlayer

# Nx
.nx/cache
