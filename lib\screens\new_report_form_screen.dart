import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:provider/provider.dart';
import 'package:signature/signature.dart';
import '../models/inspection_report.dart';
import '../services/inspection_service.dart';
import '../services/firebase_service.dart';

class NewReportFormScreen extends StatefulWidget {
  final InspectionReport? existingReport;

  const NewReportFormScreen({super.key, this.existingReport});

  @override
  State<NewReportFormScreen> createState() => _NewReportFormScreenState();
}

class _NewReportFormScreenState extends State<NewReportFormScreen> {
  final GlobalKey<FormBuilderState> _formKey = GlobalKey<FormBuilderState>();
  final InspectionService _inspectionService = InspectionService();

  bool _isLoading = false;
  bool get _isEditing => widget.existingReport != null;

  // Dynamic checklist items
  List<ChecklistItem> _checklistItems = [
    ChecklistItem(label: 'Size Spec', isChecked: false),
    ChecklistItem(label: 'Fabric Quality', isChecked: false),
    ChecklistItem(label: 'Color Matching', isChecked: false),
    ChecklistItem(label: 'Stitching Quality', isChecked: false),
    ChecklistItem(label: 'Packaging', isChecked: false),
    ChecklistItem(label: 'Labeling', isChecked: false),
  ];

  List<WorkmanshipComment> _workmanshipComments = [];
  List<MeasurementComment> _measurementComments = [];

  // Signature controllers
  late SignatureController _inspectorSignatureController;
  late SignatureController _vendorSignatureController;

  @override
  void initState() {
    super.initState();

    // Initialize signature controllers
    _inspectorSignatureController = SignatureController(
      penStrokeWidth: 2,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );

    _vendorSignatureController = SignatureController(
      penStrokeWidth: 2,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );

    if (_isEditing) {
      _loadExistingData();
    }
  }

  @override
  void dispose() {
    _inspectorSignatureController.dispose();
    _vendorSignatureController.dispose();
    super.dispose();
  }

  void _loadExistingData() {
    final report = widget.existingReport!;
    _workmanshipComments = List.from(report.workmanshipComments);
    _measurementComments = List.from(report.measurementComments);
    _checklistItems = List.from(report.checklistItems);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Edit Inspection Report' : 'New Inspection Report',
        ),
        backgroundColor: Colors.grey.shade800,
        foregroundColor: Colors.white,
        actions: [
          IconButton(onPressed: _submitForm, icon: const Icon(Icons.save)),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: FormBuilder(
                  key: _formKey,
                  initialValue: _isEditing ? _getInitialValues() : {},
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildChecklistSection(),
                      const SizedBox(height: 24),
                      _buildMaterialCommentSection(),
                      const SizedBox(height: 24),
                      _buildWorkmanshipSection(),
                      const SizedBox(height: 24),
                      _buildMeasurementSection(),
                      const SizedBox(height: 24),
                      _buildSignatureSection(),
                      const SizedBox(height: 32),
                      _buildSubmitButton(),
                    ],
                  ),
                ),
              ),
    );
  }

  Map<String, dynamic> _getInitialValues() {
    final report = widget.existingReport!;
    return {
      'date': report.date,
      'code': report.code ?? '',
      'articleName': report.articleName,
      'product': report.product ?? '',
      'vendor': report.vendor,
      'repairPercentage': report.repairPercentage?.toString() ?? '0.0',
      'productionStatus': report.productionStatus,
      'qtyOrder': report.qtyOrder.toString(),
      'qtyCheck': report.qtyCheck,
      'isInline': report.isInline,
      'isFinal': report.isFinal,
      'customCheckLabel': report.customCheckLabel ?? 'PPM',
      'isCustomCheck': report.isCustomCheck,
      'materialComment': report.materialComment,
      'note': report.note,
      'status': report.status,
    };
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade800,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'BASIC INFORMATION',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(height: 16),
            // 1 Row 1 Column Layout
            _buildFormRow(
              'Date',
              FormBuilderDateTimePicker(
                name: 'date',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                validator: FormBuilderValidators.required(),
              ),
            ),
            _buildFormRow(
              'Code (PO/Article)',
              FormBuilderTextField(
                name: 'code',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                  hintText: 'PO-2024-001 or ART-T001',
                ),
                validator: FormBuilderValidators.required(),
              ),
            ),
            _buildFormRow(
              'Article Name',
              FormBuilderTextField(
                name: 'articleName',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                validator: FormBuilderValidators.required(),
              ),
            ),
            _buildFormRow(
              'Product/Item',
              FormBuilderTextField(
                name: 'product',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                  hintText: 'Cotton Basic Tee, Denim Jeans, etc.',
                ),
                validator: FormBuilderValidators.required(),
              ),
            ),
            _buildFormRow(
              'Vendor',
              FormBuilderTextField(
                name: 'vendor',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                validator: FormBuilderValidators.required(),
              ),
            ),
            _buildFormRow(
              'Production Status',
              FormBuilderTextField(
                name: 'productionStatus',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                validator: FormBuilderValidators.required(),
              ),
            ),
            _buildFormRow(
              'Qty Order',
              FormBuilderTextField(
                name: 'qtyOrder',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                keyboardType: TextInputType.number,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                  FormBuilderValidators.integer(),
                ]),
              ),
            ),
            _buildFormRow(
              'Qty Check',
              FormBuilderTextField(
                name: 'qtyCheck',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                  hintText: 'e.g., 20, 30 pcs, 50 units',
                ),
                validator: FormBuilderValidators.required(),
              ),
            ),
            _buildFormRow(
              '%Repair',
              FormBuilderTextField(
                name: 'repairPercentage',
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  isDense: true,
                  hintText: '0.0',
                  suffixText: '%',
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                  FormBuilderValidators.numeric(),
                  FormBuilderValidators.min(0),
                  FormBuilderValidators.max(100),
                ]),
              ),
            ),
            // Inspection Type - 1 row 1 column layout
            _buildFormRow(
              'Inline',
              FormBuilderCheckbox(
                name: 'isInline',
                title: const Text('Inline'),
                initialValue: false,
              ),
            ),
            _buildFormRow(
              'Final',
              FormBuilderCheckbox(
                name: 'isFinal',
                title: const Text('Final'),
                initialValue: false,
              ),
            ),
            // Dynamic checkbox with customizable label
            _buildDynamicCheckboxRow(),
            // Report By field (read-only)
            _buildFormRow(
              'Report By',
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey.shade100,
                ),
                child: Text(
                  _isEditing
                      ? (widget.existingReport!.reportBy ?? 'Unknown User')
                      : (Provider.of<FirebaseService>(
                            context,
                            listen: false,
                          ).currentUser?.email ??
                          'Unknown User'),
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormRow(String label, Widget field) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
          ),
          const SizedBox(height: 8),
          field,
        ],
      ),
    );
  }

  Widget _buildDynamicCheckboxRow() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Custom Check',
                style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _editCustomCheckboxLabel,
                icon: const Icon(Icons.edit, size: 16),
                tooltip: 'Edit label',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: FormBuilderTextField(
                  name: 'customCheckLabel',
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    isDense: true,
                    hintText: 'PPM, TYM, Quality Check, etc.',
                  ),
                ),
              ),
              const SizedBox(width: 12),
              FormBuilderCheckbox(
                name: 'isCustomCheck',
                title: const Text(''),
                initialValue: false,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChecklistSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade800,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'CHECKLIST ITEMS',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _addChecklistItem,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Add'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            ..._checklistItems.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return _buildChecklistRow(index, item);
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildChecklistRow(int index, ChecklistItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(item.label, style: const TextStyle(fontSize: 14)),
          ),
          Checkbox(
            value: item.isChecked,
            onChanged: (value) {
              setState(() {
                _checklistItems[index] = ChecklistItem(
                  label: item.label,
                  isChecked: value ?? false,
                );
              });
            },
          ),
          IconButton(
            onPressed: () => _removeChecklistItem(index),
            icon: const Icon(Icons.delete, color: Colors.red),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  void _addChecklistItem() {
    showDialog(
      context: context,
      builder: (context) {
        String label = '';
        return AlertDialog(
          title: const Text('Add Checklist Item'),
          content: TextField(
            decoration: const InputDecoration(
              labelText: 'Item Label',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => label = value,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (label.trim().isNotEmpty) {
                  setState(() {
                    _checklistItems.add(
                      ChecklistItem(label: label.trim(), isChecked: false),
                    );
                  });
                  Navigator.pop(context);
                }
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _removeChecklistItem(int index) {
    setState(() {
      _checklistItems.removeAt(index);
    });
  }

  void _editCustomCheckboxLabel() {
    final currentLabel =
        _formKey.currentState?.fields['customCheckLabel']?.value ?? '';

    showDialog(
      context: context,
      builder: (context) {
        String label = currentLabel;
        return AlertDialog(
          title: const Text('Edit Custom Checkbox Label'),
          content: TextField(
            decoration: const InputDecoration(
              labelText: 'Checkbox Label',
              border: OutlineInputBorder(),
              hintText: 'PPM, TYM, Quality Check, etc.',
            ),
            controller: TextEditingController(text: currentLabel),
            onChanged: (value) => label = value,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (label.trim().isNotEmpty) {
                  _formKey.currentState?.fields['customCheckLabel']?.didChange(
                    label.trim(),
                  );
                }
                Navigator.pop(context);
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMaterialCommentSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade800,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'MATERIAL COMMENT',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(height: 16),
            FormBuilderTextField(
              name: 'materialComment',
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Enter material comment...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkmanshipSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade800,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'WORKMANSHIP COMMENT',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _addWorkmanshipComment,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Add'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Header with horizontal scroll
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade800,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Row(
                  children: [
                    SizedBox(
                      width: 40,
                      child: Text(
                        'No.',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 200,
                      child: Text(
                        'Comment',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 80,
                      child: Text(
                        'MJ',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(
                      width: 80,
                      child: Text(
                        'MN',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(width: 50),
                  ],
                ),
              ),
            ),
            // Data rows with horizontal scroll
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Column(
                children:
                    _workmanshipComments.asMap().entries.map((entry) {
                      final index = entry.key;
                      final comment = entry.value;
                      return _buildWorkmanshipRow(index, comment);
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkmanshipRow(int index, WorkmanshipComment comment) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
        color: Colors.white,
      ),
      child: Row(
        children: [
          SizedBox(
            width: 40,
            child: Text(
              '${index + 1}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(
            width: 200,
            child: GestureDetector(
              onTap: () => _editWorkmanshipComment(index),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey.shade50,
                ),
                child: Text(
                  comment.comment.isEmpty
                      ? 'Tap to add comment...'
                      : comment.comment,
                  style: TextStyle(
                    color: comment.comment.isEmpty ? Colors.grey : Colors.black,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 80,
            child: Transform.scale(
              scale: 1.2,
              child: Checkbox(
                value: comment.isMJ,
                onChanged: (value) {
                  setState(() {
                    _workmanshipComments[index] = WorkmanshipComment(
                      comment: comment.comment,
                      isMJ: value ?? false,
                      isMN: comment.isMN,
                    );
                  });
                },
                activeColor: Colors.blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
          SizedBox(
            width: 80,
            child: Transform.scale(
              scale: 1.2,
              child: Checkbox(
                value: comment.isMN,
                onChanged: (value) {
                  setState(() {
                    _workmanshipComments[index] = WorkmanshipComment(
                      comment: comment.comment,
                      isMJ: comment.isMJ,
                      isMN: value ?? false,
                    );
                  });
                },
                activeColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
          SizedBox(
            width: 50,
            child: IconButton(
              onPressed: () => _removeWorkmanshipComment(index),
              icon: const Icon(Icons.delete, color: Colors.red),
              iconSize: 20,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),
        ],
      ),
    );
  }

  void _addWorkmanshipComment() {
    _showWorkmanshipDialog();
  }

  void _editWorkmanshipComment(int index) {
    _showWorkmanshipDialog(index);
  }

  void _showWorkmanshipDialog([int? editIndex]) {
    final isEditing = editIndex != null;
    final existingComment = isEditing ? _workmanshipComments[editIndex] : null;

    String comment = existingComment?.comment ?? '';
    bool isMJ = existingComment?.isMJ ?? false;
    bool isMN = existingComment?.isMN ?? false;

    final commentController = TextEditingController(text: comment);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                isEditing
                    ? 'Edit Workmanship Comment'
                    : 'Add Workmanship Comment',
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Comment:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: commentController,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Enter workmanship comment...',
                      ),
                      maxLines: 4,
                      onChanged: (value) {
                        comment = value;
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Status:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('MJ'),
                            value: isMJ,
                            onChanged: (value) {
                              setDialogState(() {
                                isMJ = value ?? false;
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        ),
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('MN'),
                            value: isMN,
                            onChanged: (value) {
                              setDialogState(() {
                                isMN = value ?? false;
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (comment.trim().isNotEmpty) {
                      setState(() {
                        final newComment = WorkmanshipComment(
                          comment: comment.trim(),
                          isMJ: isMJ,
                          isMN: isMN,
                        );

                        if (isEditing) {
                          _workmanshipComments[editIndex] = newComment;
                        } else {
                          _workmanshipComments.add(newComment);
                        }
                      });
                      Navigator.of(context).pop();
                    }
                  },
                  child: Text(isEditing ? 'Update' : 'Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _removeWorkmanshipComment(int index) {
    setState(() {
      _workmanshipComments.removeAt(index);
    });
  }

  Widget _buildMeasurementSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade800,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'MEASUREMENT COMMENT',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _addMeasurementComment,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Add'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            ..._measurementComments.asMap().entries.map((entry) {
              final index = entry.key;
              final measurement = entry.value;
              return _buildMeasurementRow(index, measurement);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildMeasurementRow(int index, MeasurementComment measurement) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${index + 1}. ',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => _editMeasurementComment(index),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      measurement.keterangan.isEmpty
                          ? 'Tap to add description...'
                          : measurement.keterangan,
                      style: TextStyle(
                        color:
                            measurement.keterangan.isEmpty
                                ? Colors.grey
                                : Colors.black,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _removeMeasurementComment(index),
                icon: const Icon(Icons.delete, color: Colors.red),
                iconSize: 20,
              ),
            ],
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                for (int i = 0; i < 5; i++)
                  Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 8),
                    child: Column(
                      children: [
                        Text(
                          '${20 + (i * 5)}L',
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            i < measurement.measurements.length
                                ? measurement.measurements[i].toString()
                                : '0.0',
                            textAlign: TextAlign.center,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  ),
                Container(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    measurement.unit,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addMeasurementComment() {
    _showMeasurementDialog();
  }

  void _editMeasurementComment(int index) {
    _showMeasurementDialog(index);
  }

  void _showMeasurementDialog([int? editIndex]) {
    final isEditing = editIndex != null;
    final existingComment = isEditing ? _measurementComments[editIndex] : null;

    String keterangan = existingComment?.keterangan ?? '';
    String selectedUnit = existingComment?.unit ?? 'cm';
    List<double> measurements =
        existingComment?.measurements ?? [0.0, 0.0, 0.0, 0.0, 0.0];

    final keteranganController = TextEditingController(text: keterangan);
    final measurementControllers = List.generate(
      5,
      (index) => TextEditingController(
        text:
            index < measurements.length
                ? measurements[index].toString()
                : '0.0',
      ),
    );

    // Available units
    final List<String> availableUnits = [
      'cm',
      'mm',
      'm',
      'inch',
      'ft',
      'yard',
      'kg',
      'g',
      'lb',
      'oz',
      'pcs',
      '%',
    ];

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                isEditing
                    ? 'Edit Measurement Comment'
                    : 'Add Measurement Comment',
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Description:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: keteranganController,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Enter measurement description...',
                      ),
                      maxLines: 2,
                      onChanged: (value) {
                        keterangan = value;
                      },
                    ),
                    const SizedBox(height: 16),
                    // Unit selector
                    Row(
                      children: [
                        const Text(
                          'Unit: ',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: selectedUnit,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              isDense: true,
                            ),
                            items:
                                availableUnits.map((String unit) {
                                  return DropdownMenuItem<String>(
                                    value: unit,
                                    child: Text(unit),
                                  );
                                }).toList(),
                            onChanged: (String? newValue) {
                              setDialogState(() {
                                selectedUnit = newValue ?? 'cm';
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Measurements ($selectedUnit):',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    for (int i = 0; i < 5; i++)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 60,
                              child: Text(
                                '${20 + (i * 5)}L:',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            Expanded(
                              child: TextField(
                                controller: measurementControllers[i],
                                decoration: const InputDecoration(
                                  border: OutlineInputBorder(),
                                  isDense: true,
                                ),
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                      decimal: true,
                                    ),
                                onChanged: (value) {
                                  measurements[i] =
                                      double.tryParse(value) ?? 0.0;
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (keterangan.trim().isNotEmpty) {
                      setState(() {
                        final newComment = MeasurementComment(
                          number:
                              isEditing
                                  ? existingComment!.number
                                  : _measurementComments.length + 1,
                          keterangan: keterangan.trim(),
                          measurements: measurements,
                          unit: selectedUnit,
                        );

                        if (isEditing) {
                          _measurementComments[editIndex] = newComment;
                        } else {
                          _measurementComments.add(newComment);
                        }
                      });
                      Navigator.of(context).pop();
                    }
                  },
                  child: Text(isEditing ? 'Update' : 'Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _removeMeasurementComment(int index) {
    setState(() {
      _measurementComments.removeAt(index);
    });
  }

  Widget _buildSignatureSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade800,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'SIGNATURE & STATUS',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Inspector Signature Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Inspector Signature:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () => _openSignatureDialog('inspector'),
                        child: Container(
                          height: 120,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(4),
                            color: Colors.grey.shade50,
                          ),
                          child:
                              _inspectorSignatureController.isEmpty
                                  ? const Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.touch_app,
                                          size: 32,
                                          color: Colors.grey,
                                        ),
                                        SizedBox(height: 8),
                                        Text(
                                          'Tap to Sign',
                                          style: TextStyle(
                                            color: Colors.grey,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                  : ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: Signature(
                                      controller: _inspectorSignatureController,
                                      backgroundColor: Colors.white,
                                    ),
                                  ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton.icon(
                            onPressed:
                                _inspectorSignatureController.isEmpty
                                    ? null
                                    : () =>
                                        _inspectorSignatureController.clear(),
                            icon: const Icon(Icons.clear, size: 16),
                            label: const Text('Clear'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // Vendor Signature Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Vendor Signature:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () => _openSignatureDialog('vendor'),
                        child: Container(
                          height: 120,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(4),
                            color: Colors.grey.shade50,
                          ),
                          child:
                              _vendorSignatureController.isEmpty
                                  ? const Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.touch_app,
                                          size: 32,
                                          color: Colors.grey,
                                        ),
                                        SizedBox(height: 8),
                                        Text(
                                          'Tap to Sign',
                                          style: TextStyle(
                                            color: Colors.grey,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                  : ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: Signature(
                                      controller: _vendorSignatureController,
                                      backgroundColor: Colors.white,
                                    ),
                                  ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton.icon(
                            onPressed:
                                _vendorSignatureController.isEmpty
                                    ? null
                                    : () => _vendorSignatureController.clear(),
                            icon: const Icon(Icons.clear, size: 16),
                            label: const Text('Clear'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Note Section - New Row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Note:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      FormBuilderTextField(
                        name: 'note',
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: 'Enter additional notes...',
                          isDense: true,
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Status Section - Below Note
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Status:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      FormBuilderRadioGroup(
                        name: 'status',
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                        ),
                        options: const [
                          FormBuilderFieldOption(
                            value: InspectionStatus.pass,
                            child: Text('☐ PASS'),
                          ),
                          FormBuilderFieldOption(
                            value: InspectionStatus.hold,
                            child: Text('☐ HOLD'),
                          ),
                          FormBuilderFieldOption(
                            value: InspectionStatus.fail,
                            child: Text('☐ FAIL'),
                          ),
                        ],
                        validator: FormBuilderValidators.required(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _openSignatureDialog(String type) {
    final controller =
        type == 'inspector'
            ? _inspectorSignatureController
            : _vendorSignatureController;

    final title =
        type == 'inspector' ? 'Inspector Signature' : 'Vendor Signature';

    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 300,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Signature(
                        controller: controller,
                        backgroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => controller.clear(),
                        icon: const Icon(Icons.clear, size: 16),
                        label: const Text('Clear'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {}); // Refresh to show signature
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.check, size: 16),
                        label: const Text('Done'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _submitForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: Text(
          _isEditing ? 'Update Report' : 'Save Report',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.saveAndValidate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final formData = _formKey.currentState!.value;
        final firebaseService = Provider.of<FirebaseService>(
          context,
          listen: false,
        );
        final currentUser = firebaseService.currentUser;
        final reportBy = currentUser?.email ?? 'Unknown User';

        final report = InspectionReport(
          id:
              _isEditing
                  ? widget.existingReport!.id
                  : DateTime.now().millisecondsSinceEpoch.toString(),
          date: formData['date'] as DateTime,
          code: formData['code'] as String?,
          articleName: formData['articleName'] as String,
          product: formData['product'] as String?,
          vendor: formData['vendor'] as String,
          repairPercentage:
              formData['repairPercentage'] != null
                  ? double.parse(formData['repairPercentage'])
                  : null,
          productionStatus: formData['productionStatus'] as String,
          qtyOrder: int.parse(formData['qtyOrder']),
          qtyCheck: formData['qtyCheck'] as String,
          isInline: formData['isInline'] as bool? ?? false,
          isFinal: formData['isFinal'] as bool? ?? false,
          customCheckLabel: formData['customCheckLabel'] as String?,
          isCustomCheck: formData['isCustomCheck'] as bool? ?? false,
          materialComment: formData['materialComment'] as String? ?? '',
          workmanshipComments: _workmanshipComments,
          measurementComments: _measurementComments,
          checklistItems: _checklistItems,
          note: formData['note'] as String? ?? '',
          status: formData['status'] as InspectionStatus,
          reportBy: reportBy,
        );

        if (_isEditing) {
          await _inspectionService.updateReport(report);
        } else {
          await _inspectionService.addReport(report);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isEditing
                    ? 'Report updated successfully!'
                    : 'Report saved successfully!',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
